<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>安全电子邮件服务 - 交互式学习</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/p5.js/1.7.0/p5.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .section {
            background: rgba(255, 255, 255, 0.95);
            margin: 30px 0;
            padding: 40px;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            opacity: 0;
            transform: translateY(50px);
        }

        .question-box {
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            color: white;
            padding: 30px;
            border-radius: 15px;
            margin-bottom: 30px;
            text-align: center;
        }

        .question-title {
            font-size: 2.5em;
            margin-bottom: 20px;
            font-weight: bold;
        }

        .question-content {
            font-size: 1.3em;
            line-height: 1.6;
        }

        .options {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
            margin: 20px 0;
        }

        .option {
            padding: 15px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }

        .option:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }

        .step-title {
            font-size: 2em;
            color: #2c3e50;
            margin-bottom: 20px;
            text-align: center;
            position: relative;
        }

        .step-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 100px;
            height: 3px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        .concept-card {
            background: linear-gradient(135deg, #74b9ff, #0984e3);
            color: white;
            padding: 25px;
            border-radius: 15px;
            margin: 20px 0;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .concept-card::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
            transform: scale(0);
            transition: transform 0.6s ease;
        }

        .concept-card:hover::before {
            transform: scale(1);
        }

        .concept-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
        }

        .interactive-demo {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 30px;
            margin: 20px 0;
            text-align: center;
        }

        .email-flow {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 30px 0;
            flex-wrap: wrap;
        }

        .email-node {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
        }

        .sender { background: linear-gradient(135deg, #fd79a8, #e84393); }
        .encryption { background: linear-gradient(135deg, #fdcb6e, #e17055); }
        .transmission { background: linear-gradient(135deg, #6c5ce7, #a29bfe); }
        .receiver { background: linear-gradient(135deg, #00b894, #00cec9); }

        .arrow {
            font-size: 2em;
            color: #ddd;
            margin: 0 10px;
        }

        .btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1.1em;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e0e0e0;
            border-radius: 4px;
            margin: 20px 0;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            width: 0%;
            transition: width 0.5s ease;
        }

        .canvas-container {
            margin: 20px 0;
            text-align: center;
        }

        @media (max-width: 768px) {
            .email-flow {
                flex-direction: column;
            }
            
            .arrow {
                transform: rotate(90deg);
                margin: 10px 0;
            }
            
            .options {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 题目展示区 -->
        <div class="section" id="question-section">
            <div class="question-box">
                <div class="question-title">📧 安全电子邮件服务</div>
                <div class="question-content">
                    <p><strong>题目：</strong>下面可提供安全电子邮件服务的是（ ）。</p>
                    <div class="options">
                        <div class="option" data-answer="A">A. RSA</div>
                        <div class="option" data-answer="B">B. SSL</div>
                        <div class="option" data-answer="C">C. SET</div>
                        <div class="option" data-answer="D">D. S/MIME</div>
                    </div>
                    <p style="margin-top: 20px;"><strong>正确答案：D</strong></p>
                </div>
            </div>
        </div>

        <!-- 基础概念讲解 -->
        <div class="section" id="concept-section">
            <div class="step-title">🎯 第一步：理解电子邮件的基本概念</div>
            
            <div class="concept-card" onclick="showEmailBasics()">
                <h3>📨 什么是电子邮件？</h3>
                <p>点击探索电子邮件的工作原理</p>
            </div>

            <div class="interactive-demo" id="email-demo" style="display: none;">
                <h4>电子邮件传输过程</h4>
                <div class="email-flow">
                    <div class="email-node sender" onclick="animateNode(this)">
                        <span>发送者<br>👤</span>
                    </div>
                    <div class="arrow">→</div>
                    <div class="email-node transmission" onclick="animateNode(this)">
                        <span>网络传输<br>🌐</span>
                    </div>
                    <div class="arrow">→</div>
                    <div class="email-node receiver" onclick="animateNode(this)">
                        <span>接收者<br>👥</span>
                    </div>
                </div>
                <button class="btn" onclick="startEmailAnimation()">观看完整传输过程</button>
            </div>
        </div>

        <!-- 安全问题讲解 -->
        <div class="section" id="security-section">
            <div class="step-title">🔒 第二步：电子邮件的安全问题</div>
            
            <div class="concept-card" onclick="showSecurityIssues()">
                <h3>⚠️ 电子邮件面临哪些安全威胁？</h3>
                <p>点击了解邮件安全的重要性</p>
            </div>

            <div class="canvas-container">
                <div id="security-canvas"></div>
            </div>
        </div>

        <!-- 各选项详解 -->
        <div class="section" id="options-section">
            <div class="step-title">🔍 第三步：逐一分析各个选项</div>
            
            <div class="concept-card" onclick="explainOption('RSA')">
                <h3>A. RSA - 加密算法</h3>
                <p>点击了解RSA的作用和局限</p>
            </div>

            <div class="concept-card" onclick="explainOption('SSL')">
                <h3>B. SSL - 传输层安全</h3>
                <p>点击了解SSL的工作原理</p>
            </div>

            <div class="concept-card" onclick="explainOption('SET')">
                <h3>C. SET - 电子交易标准</h3>
                <p>点击了解SET的应用场景</p>
            </div>

            <div class="concept-card" onclick="explainOption('SMIME')">
                <h3>D. S/MIME - 安全邮件扩展 ✅</h3>
                <p>点击了解为什么这是正确答案</p>
            </div>
        </div>

        <!-- S/MIME详细讲解 -->
        <div class="section" id="smime-section">
            <div class="step-title">🎯 第四步：深入理解S/MIME</div>

            <div class="interactive-demo">
                <h4>S/MIME工作原理演示</h4>
                <div class="email-flow">
                    <div class="email-node sender" onclick="demonstrateSMIME('compose')">
                        <span>编写邮件<br>✍️</span>
                    </div>
                    <div class="arrow">→</div>
                    <div class="email-node encryption" onclick="demonstrateSMIME('encrypt')">
                        <span>S/MIME<br>加密签名<br>🔐</span>
                    </div>
                    <div class="arrow">→</div>
                    <div class="email-node transmission" onclick="demonstrateSMIME('send')">
                        <span>安全传输<br>📤</span>
                    </div>
                    <div class="arrow">→</div>
                    <div class="email-node receiver" onclick="demonstrateSMIME('receive')">
                        <span>验证解密<br>📥</span>
                    </div>
                </div>
                <button class="btn" onclick="startSMIMEDemo()">完整演示S/MIME流程</button>
            </div>

            <div class="concept-card" onclick="showSMIMEFeatures()">
                <h3>🛡️ S/MIME的四大安全功能</h3>
                <p>点击了解S/MIME如何全面保护邮件安全</p>
            </div>
        </div>

        <!-- 对比学习区 -->
        <div class="section" id="comparison-section">
            <div class="step-title">⚖️ 第五步：对比学习 - 为什么选择S/MIME？</div>

            <div class="interactive-demo">
                <h4>安全技术对比表</h4>
                <div id="comparison-table"></div>
                <button class="btn" onclick="showComparison()">显示详细对比</button>
            </div>
        </div>

        <!-- 实践练习区 -->
        <div class="section" id="practice-section">
            <div class="step-title">🎮 第六步：互动练习</div>

            <div class="interactive-demo">
                <h4>情景判断题</h4>
                <div id="scenario-quiz"></div>
                <button class="btn" onclick="startQuiz()">开始练习</button>
            </div>
        </div>

        <!-- 总结区 -->
        <div class="section" id="summary-section">
            <div class="step-title">🎓 学习总结</div>

            <div class="concept-card">
                <h3>📝 知识要点回顾</h3>
                <ul style="text-align: left; margin: 20px 0;">
                    <li><strong>RSA：</strong>加密算法，不是完整邮件安全方案</li>
                    <li><strong>SSL：</strong>传输层安全，保护数据传输过程</li>
                    <li><strong>SET：</strong>电子商务安全标准，非邮件专用</li>
                    <li><strong>S/MIME：</strong>专门的安全邮件扩展，提供完整邮件安全服务</li>
                </ul>
            </div>

            <div class="interactive-demo">
                <h4>🏆 学习成就</h4>
                <div id="achievement-display"></div>
                <button class="btn" onclick="showAchievements()">查看我的学习成果</button>
            </div>
        </div>

        <!-- 进度条 -->
        <div class="progress-bar">
            <div class="progress-fill" id="progress"></div>
        </div>
    </div>

    <script>
        let currentStep = 0;
        const totalSteps = 6;
        let p5Instance;
        let userProgress = {
            conceptsLearned: [],
            quizScore: 0,
            timeSpent: 0,
            interactionsCount: 0
        };
        let startTime = Date.now();

        // 初始化动画
        gsap.registerPlugin();
        
        // 页面加载动画
        window.addEventListener('load', () => {
            gsap.to('.section', {
                opacity: 1,
                y: 0,
                duration: 0.8,
                stagger: 0.2,
                ease: "power2.out"
            });
            updateProgress();
        });

        // 更新进度条
        function updateProgress() {
            const progress = (currentStep / totalSteps) * 100;
            gsap.to('#progress', {
                width: `${progress}%`,
                duration: 0.5,
                ease: "power2.out"
            });
        }

        // 显示邮件基础知识
        function showEmailBasics() {
            const demo = document.getElementById('email-demo');
            demo.style.display = 'block';
            gsap.fromTo(demo, 
                { opacity: 0, y: 20 },
                { opacity: 1, y: 0, duration: 0.5 }
            );
            currentStep = Math.max(currentStep, 1);
            updateProgress();
        }

        // 节点动画
        function animateNode(node) {
            gsap.to(node, {
                scale: 1.2,
                duration: 0.2,
                yoyo: true,
                repeat: 1,
                ease: "power2.inOut"
            });
        }

        // 邮件传输动画
        function startEmailAnimation() {
            const nodes = document.querySelectorAll('.email-node');
            const arrows = document.querySelectorAll('.arrow');
            
            // 重置状态
            gsap.set(nodes, { scale: 1, opacity: 0.3 });
            gsap.set(arrows, { opacity: 0.3 });
            
            // 动画序列
            const tl = gsap.timeline();
            
            tl.to(nodes[0], { opacity: 1, scale: 1.1, duration: 0.5 })
              .to(arrows[0], { opacity: 1, duration: 0.3 }, "-=0.2")
              .to(nodes[1], { opacity: 1, scale: 1.1, duration: 0.5 })
              .to(arrows[1], { opacity: 1, duration: 0.3 }, "-=0.2")
              .to(nodes[2], { opacity: 1, scale: 1.1, duration: 0.5 });
        }

        // 显示安全问题
        function showSecurityIssues() {
            if (!p5Instance) {
                // 创建p5.js画布来展示安全威胁
                const sketch = (p) => {
                    let threats = [];
                    let email;
                    
                    p.setup = () => {
                        const canvas = p.createCanvas(800, 400);
                        canvas.parent('security-canvas');
                        
                        // 初始化威胁
                        threats = [
                            { x: 100, y: 100, name: '窃听', color: [255, 100, 100] },
                            { x: 300, y: 150, name: '篡改', color: [255, 150, 100] },
                            { x: 500, y: 120, name: '伪造', color: [255, 200, 100] },
                            { x: 700, y: 180, name: '否认', color: [255, 100, 200] }
                        ];
                        
                        email = { x: 400, y: 300, size: 60 };
                    };
                    
                    p.draw = () => {
                        p.background(248, 249, 250);
                        
                        // 绘制邮件
                        p.fill(100, 150, 255);
                        p.ellipse(email.x, email.y, email.size);
                        p.fill(255);
                        p.textAlign(p.CENTER, p.CENTER);
                        p.text('📧', email.x, email.y);
                        
                        // 绘制威胁
                        threats.forEach((threat, i) => {
                            p.fill(threat.color[0], threat.color[1], threat.color[2], 150);
                            p.ellipse(threat.x, threat.y, 80);
                            
                            // 威胁向邮件移动
                            threat.x += (email.x - threat.x) * 0.005;
                            threat.y += (email.y - threat.y) * 0.005;
                            
                            p.fill(0);
                            p.textAlign(p.CENTER, p.CENTER);
                            p.text(threat.name, threat.x, threat.y);
                            
                            // 绘制攻击线
                            p.stroke(threat.color[0], threat.color[1], threat.color[2], 100);
                            p.line(threat.x, threat.y, email.x, email.y);
                            p.noStroke();
                        });
                    };
                };
                
                p5Instance = new p5(sketch);
            }
            
            currentStep = Math.max(currentStep, 2);
            updateProgress();
        }

        // 解释各选项
        function explainOption(option) {
            let explanation = '';
            let isCorrect = false;
            
            switch(option) {
                case 'RSA':
                    explanation = `
                        <h4>🔐 RSA加密算法</h4>
                        <p><strong>作用：</strong>RSA是一种非对称加密算法，用于数据加密和数字签名。</p>
                        <p><strong>局限：</strong>RSA只是一个加密工具，不是完整的邮件安全解决方案。</p>
                        <p><strong>结论：</strong>❌ 不是专门的邮件安全服务</p>
                    `;
                    break;
                case 'SSL':
                    explanation = `
                        <h4>🔒 SSL/TLS传输层安全</h4>
                        <p><strong>作用：</strong>SSL用于保护网络传输过程中的数据安全。</p>
                        <p><strong>应用：</strong>主要用于HTTPS、邮件传输等场景的传输加密。</p>
                        <p><strong>结论：</strong>❌ 保护传输过程，但不是专门的邮件内容安全服务</p>
                    `;
                    break;
                case 'SET':
                    explanation = `
                        <h4>💳 SET安全电子交易</h4>
                        <p><strong>作用：</strong>SET是专门为电子商务交易设计的安全标准。</p>
                        <p><strong>应用：</strong>主要用于信用卡在线支付的安全保护。</p>
                        <p><strong>结论：</strong>❌ 专注于电子商务，不是邮件安全服务</p>
                    `;
                    break;
                case 'SMIME':
                    explanation = `
                        <h4>✅ S/MIME安全多用途互联网邮件扩展</h4>
                        <p><strong>专门设计：</strong>S/MIME是专门为电子邮件安全设计的标准。</p>
                        <p><strong>功能完整：</strong>提供加密、数字签名、身份认证等完整邮件安全服务。</p>
                        <p><strong>广泛应用：</strong>被各大邮件客户端广泛支持和使用。</p>
                        <p><strong>结论：</strong>✅ 这是正确答案！</p>
                    `;
                    isCorrect = true;
                    break;
            }
            
            // 创建弹窗显示解释
            const popup = document.createElement('div');
            popup.style.cssText = `
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: white;
                padding: 30px;
                border-radius: 15px;
                box-shadow: 0 20px 40px rgba(0,0,0,0.3);
                z-index: 1000;
                max-width: 500px;
                width: 90%;
            `;
            
            popup.innerHTML = `
                ${explanation}
                <button class="btn" onclick="this.parentElement.remove(); updateStepProgress()">
                    ${isCorrect ? '🎉 太棒了！' : '📚 继续学习'}
                </button>
            `;
            
            document.body.appendChild(popup);
            
            // 添加背景遮罩
            const overlay = document.createElement('div');
            overlay.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.5);
                z-index: 999;
            `;
            overlay.onclick = () => {
                popup.remove();
                overlay.remove();
            };
            document.body.appendChild(overlay);
            
            // 动画显示
            gsap.fromTo(popup, 
                { opacity: 0, scale: 0.8 },
                { opacity: 1, scale: 1, duration: 0.3 }
            );
        }

        function updateStepProgress() {
            currentStep = Math.max(currentStep, 3);
            updateProgress();
        }

        // S/MIME演示功能
        function demonstrateSMIME(stage) {
            userProgress.interactionsCount++;
            let message = '';

            switch(stage) {
                case 'compose':
                    message = '📝 用户编写邮件内容，准备发送给接收者';
                    break;
                case 'encrypt':
                    message = '🔐 S/MIME对邮件进行数字签名和加密，确保安全性';
                    break;
                case 'send':
                    message = '📤 加密后的邮件通过网络安全传输';
                    break;
                case 'receive':
                    message = '📥 接收者验证签名并解密邮件，确认发送者身份';
                    break;
            }

            showToast(message);
        }

        function startSMIMEDemo() {
            const nodes = document.querySelectorAll('#smime-section .email-node');

            gsap.set(nodes, { scale: 1, opacity: 0.3 });

            const tl = gsap.timeline();

            nodes.forEach((node, index) => {
                tl.to(node, {
                    opacity: 1,
                    scale: 1.2,
                    duration: 0.8,
                    ease: "bounce.out"
                }, index * 0.5)
                .call(() => {
                    const stages = ['compose', 'encrypt', 'send', 'receive'];
                    demonstrateSMIME(stages[index]);
                }, null, index * 0.5 + 0.4);
            });

            currentStep = Math.max(currentStep, 4);
            updateProgress();
        }

        // 显示S/MIME功能
        function showSMIMEFeatures() {
            const features = [
                { name: '数据加密', desc: '保护邮件内容不被窃取', icon: '🔒' },
                { name: '数字签名', desc: '验证发送者身份', icon: '✍️' },
                { name: '完整性保护', desc: '确保邮件未被篡改', icon: '🛡️' },
                { name: '不可否认', desc: '发送者无法否认已发送', icon: '📋' }
            ];

            showFeaturePopup(features);
            userProgress.conceptsLearned.push('S/MIME功能');
        }

        // 显示对比表
        function showComparison() {
            const comparisonData = [
                { tech: 'RSA', purpose: '加密算法', emailFocus: '❌', completeness: '部分' },
                { tech: 'SSL', purpose: '传输安全', emailFocus: '❌', completeness: '传输层' },
                { tech: 'SET', purpose: '电商安全', emailFocus: '❌', completeness: '支付专用' },
                { tech: 'S/MIME', purpose: '邮件安全', emailFocus: '✅', completeness: '完整方案' }
            ];

            const table = document.getElementById('comparison-table');
            table.innerHTML = `
                <table style="width: 100%; border-collapse: collapse; margin: 20px 0;">
                    <thead>
                        <tr style="background: #667eea; color: white;">
                            <th style="padding: 15px; border: 1px solid #ddd;">技术</th>
                            <th style="padding: 15px; border: 1px solid #ddd;">主要用途</th>
                            <th style="padding: 15px; border: 1px solid #ddd;">邮件专用</th>
                            <th style="padding: 15px; border: 1px solid #ddd;">解决方案完整性</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${comparisonData.map(row => `
                            <tr style="transition: background 0.3s;">
                                <td style="padding: 15px; border: 1px solid #ddd; font-weight: bold;">${row.tech}</td>
                                <td style="padding: 15px; border: 1px solid #ddd;">${row.purpose}</td>
                                <td style="padding: 15px; border: 1px solid #ddd; text-align: center; font-size: 1.2em;">${row.emailFocus}</td>
                                <td style="padding: 15px; border: 1px solid #ddd;">${row.completeness}</td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            `;

            // 高亮正确答案行
            const rows = table.querySelectorAll('tbody tr');
            rows[3].style.background = 'rgba(0, 184, 148, 0.1)';
            rows[3].style.border = '2px solid #00b894';

            currentStep = Math.max(currentStep, 5);
            updateProgress();
        }

        // 开始测验
        function startQuiz() {
            const scenarios = [
                {
                    question: '公司需要确保重要邮件的发送者身份真实性，应该使用：',
                    options: ['RSA加密', 'SSL证书', 'S/MIME数字签名', 'SET协议'],
                    correct: 2,
                    explanation: 'S/MIME的数字签名功能可以验证发送者身份'
                },
                {
                    question: '要保护邮件内容在传输过程中不被窃取，最适合的是：',
                    options: ['明文传输', 'S/MIME加密', '仅使用SSL', '压缩文件'],
                    correct: 1,
                    explanation: 'S/MIME提供端到端的邮件内容加密保护'
                },
                {
                    question: '以下哪个不是S/MIME的主要功能：',
                    options: ['数据加密', '数字签名', '网页浏览安全', '完整性验证'],
                    correct: 2,
                    explanation: '网页浏览安全主要由SSL/TLS提供，不是S/MIME的功能'
                }
            ];

            showQuizInterface(scenarios);
        }

        // 显示成就
        function showAchievements() {
            userProgress.timeSpent = Math.floor((Date.now() - startTime) / 1000);

            const achievements = [
                {
                    name: '知识探索者',
                    desc: `学习了 ${userProgress.conceptsLearned.length} 个概念`,
                    icon: '🎓',
                    earned: userProgress.conceptsLearned.length >= 3
                },
                {
                    name: '互动达人',
                    desc: `完成了 ${userProgress.interactionsCount} 次交互`,
                    icon: '🎮',
                    earned: userProgress.interactionsCount >= 10
                },
                {
                    name: '时间管理师',
                    desc: `学习时长 ${Math.floor(userProgress.timeSpent / 60)} 分钟`,
                    icon: '⏰',
                    earned: userProgress.timeSpent >= 300
                },
                {
                    name: 'S/MIME专家',
                    desc: '完全掌握S/MIME知识',
                    icon: '🏆',
                    earned: currentStep >= totalSteps
                }
            ];

            const display = document.getElementById('achievement-display');
            display.innerHTML = achievements.map(achievement => `
                <div style="
                    display: inline-block;
                    margin: 10px;
                    padding: 20px;
                    border-radius: 15px;
                    background: ${achievement.earned ? 'linear-gradient(135deg, #00b894, #00cec9)' : '#f0f0f0'};
                    color: ${achievement.earned ? 'white' : '#666'};
                    text-align: center;
                    min-width: 150px;
                    transition: all 0.3s ease;
                ">
                    <div style="font-size: 2em; margin-bottom: 10px;">${achievement.icon}</div>
                    <div style="font-weight: bold; margin-bottom: 5px;">${achievement.name}</div>
                    <div style="font-size: 0.9em;">${achievement.desc}</div>
                    ${achievement.earned ? '<div style="margin-top: 10px;">✅ 已获得</div>' : '<div style="margin-top: 10px;">🔒 未解锁</div>'}
                </div>
            `).join('');

            currentStep = Math.max(currentStep, 6);
            updateProgress();
        }

        // 辅助函数
        function showToast(message) {
            const toast = document.createElement('div');
            toast.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: rgba(0, 0, 0, 0.8);
                color: white;
                padding: 15px 20px;
                border-radius: 10px;
                z-index: 1001;
                max-width: 300px;
            `;
            toast.textContent = message;
            document.body.appendChild(toast);

            gsap.fromTo(toast,
                { opacity: 0, x: 100 },
                { opacity: 1, x: 0, duration: 0.3 }
            );

            setTimeout(() => {
                gsap.to(toast, {
                    opacity: 0,
                    x: 100,
                    duration: 0.3,
                    onComplete: () => toast.remove()
                });
            }, 3000);
        }

        function showFeaturePopup(features) {
            const popup = document.createElement('div');
            popup.style.cssText = `
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: white;
                padding: 30px;
                border-radius: 15px;
                box-shadow: 0 20px 40px rgba(0,0,0,0.3);
                z-index: 1000;
                max-width: 600px;
                width: 90%;
            `;

            popup.innerHTML = `
                <h3 style="text-align: center; margin-bottom: 20px;">🛡️ S/MIME的四大安全功能</h3>
                <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 20px;">
                    ${features.map(feature => `
                        <div style="
                            padding: 20px;
                            border-radius: 10px;
                            background: linear-gradient(135deg, #74b9ff, #0984e3);
                            color: white;
                            text-align: center;
                        ">
                            <div style="font-size: 2em; margin-bottom: 10px;">${feature.icon}</div>
                            <h4>${feature.name}</h4>
                            <p style="margin-top: 10px; font-size: 0.9em;">${feature.desc}</p>
                        </div>
                    `).join('')}
                </div>
                <button class="btn" onclick="this.parentElement.remove(); document.querySelector('.overlay').remove();" style="margin-top: 20px; display: block; margin-left: auto; margin-right: auto;">
                    理解了！
                </button>
            `;

            const overlay = document.createElement('div');
            overlay.className = 'overlay';
            overlay.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.5);
                z-index: 999;
            `;

            document.body.appendChild(overlay);
            document.body.appendChild(popup);

            gsap.fromTo(popup,
                { opacity: 0, scale: 0.8 },
                { opacity: 1, scale: 1, duration: 0.3 }
            );
        }

        function showQuizInterface(scenarios) {
            let currentQuestion = 0;
            let score = 0;

            const quizContainer = document.getElementById('scenario-quiz');

            function displayQuestion() {
                const scenario = scenarios[currentQuestion];
                quizContainer.innerHTML = `
                    <div style="background: white; padding: 25px; border-radius: 15px; margin: 20px 0;">
                        <h4>问题 ${currentQuestion + 1}/${scenarios.length}</h4>
                        <p style="margin: 15px 0; font-size: 1.1em;">${scenario.question}</p>
                        <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 10px; margin: 20px 0;">
                            ${scenario.options.map((option, index) => `
                                <button class="quiz-option" onclick="selectAnswer(${index})" style="
                                    padding: 15px;
                                    border: 2px solid #ddd;
                                    border-radius: 10px;
                                    background: white;
                                    cursor: pointer;
                                    transition: all 0.3s ease;
                                ">
                                    ${String.fromCharCode(65 + index)}. ${option}
                                </button>
                            `).join('')}
                        </div>
                        <div id="quiz-feedback" style="margin-top: 20px;"></div>
                    </div>
                `;
            }

            window.selectAnswer = function(selectedIndex) {
                const scenario = scenarios[currentQuestion];
                const feedback = document.getElementById('quiz-feedback');
                const options = document.querySelectorAll('.quiz-option');

                options.forEach((option, index) => {
                    option.disabled = true;
                    if (index === scenario.correct) {
                        option.style.background = '#00b894';
                        option.style.color = 'white';
                        option.style.border = '2px solid #00b894';
                    } else if (index === selectedIndex && index !== scenario.correct) {
                        option.style.background = '#e17055';
                        option.style.color = 'white';
                        option.style.border = '2px solid #e17055';
                    }
                });

                if (selectedIndex === scenario.correct) {
                    score++;
                    feedback.innerHTML = `<div style="color: #00b894; font-weight: bold;">✅ 正确！${scenario.explanation}</div>`;
                } else {
                    feedback.innerHTML = `<div style="color: #e17055; font-weight: bold;">❌ 错误。${scenario.explanation}</div>`;
                }

                setTimeout(() => {
                    currentQuestion++;
                    if (currentQuestion < scenarios.length) {
                        displayQuestion();
                    } else {
                        showQuizResults();
                    }
                }, 2000);
            };

            function showQuizResults() {
                userProgress.quizScore = score;
                const percentage = Math.round((score / scenarios.length) * 100);

                quizContainer.innerHTML = `
                    <div style="text-align: center; padding: 30px;">
                        <h3>🎉 测验完成！</h3>
                        <div style="font-size: 3em; margin: 20px 0;">
                            ${percentage >= 80 ? '🏆' : percentage >= 60 ? '🥈' : '📚'}
                        </div>
                        <p style="font-size: 1.2em; margin: 15px 0;">
                            得分：${score}/${scenarios.length} (${percentage}%)
                        </p>
                        <p style="margin: 15px 0;">
                            ${percentage >= 80 ? '优秀！你已经完全掌握了S/MIME的知识！' :
                              percentage >= 60 ? '不错！继续加油，你就能完全掌握了！' :
                              '需要再复习一下哦，重新学习相关概念吧！'}
                        </p>
                        <button class="btn" onclick="startQuiz()">重新测验</button>
                    </div>
                `;

                currentStep = Math.max(currentStep, 6);
                updateProgress();
            }

            displayQuestion();
        }

        // 选项点击事件
        document.querySelectorAll('.option').forEach(option => {
            option.addEventListener('click', function() {
                const answer = this.dataset.answer;
                userProgress.interactionsCount++;

                // 重置所有选项样式
                document.querySelectorAll('.option').forEach(opt => {
                    opt.style.border = '2px solid transparent';
                    opt.style.background = 'rgba(255, 255, 255, 0.2)';
                });

                // 高亮选中选项
                if (answer === 'D') {
                    this.style.border = '2px solid #00b894';
                    this.style.background = 'rgba(0, 184, 148, 0.3)';

                    // 添加正确答案动画
                    gsap.to(this, {
                        scale: 1.05,
                        duration: 0.3,
                        yoyo: true,
                        repeat: 1
                    });
                } else {
                    this.style.border = '2px solid #e17055';
                    this.style.background = 'rgba(225, 112, 85, 0.3)';

                    // 添加错误答案动画
                    gsap.to(this, {
                        x: [-10, 10, -10, 10, 0],
                        duration: 0.5
                    });
                }

                // 显示详细反馈
                setTimeout(() => {
                    if (answer === 'D') {
                        showToast('🎉 正确！S/MIME确实是专门的安全电子邮件服务！');
                        userProgress.conceptsLearned.push('正确答案识别');
                    } else {
                        const wrongAnswerFeedback = {
                            'A': 'RSA只是加密算法，不是完整的邮件安全方案',
                            'B': 'SSL主要用于传输层安全，不是专门的邮件服务',
                            'C': 'SET是电子商务安全标准，不是邮件专用'
                        };
                        showToast(`❌ ${wrongAnswerFeedback[answer]}，再想想哪个是专门为邮件设计的？`);
                    }
                }, 300);
            });
        });

        // 添加键盘快捷键支持
        document.addEventListener('keydown', function(e) {
            if (e.key >= '1' && e.key <= '4') {
                const optionIndex = parseInt(e.key) - 1;
                const options = document.querySelectorAll('.option');
                if (options[optionIndex]) {
                    options[optionIndex].click();
                }
            }
        });

        // 添加滚动进度指示
        window.addEventListener('scroll', function() {
            const scrolled = window.pageYOffset;
            const maxHeight = document.documentElement.scrollHeight - window.innerHeight;
            const scrollProgress = (scrolled / maxHeight) * 100;

            // 更新页面滚动进度
            const scrollIndicator = document.createElement('div');
            if (!document.getElementById('scroll-indicator')) {
                scrollIndicator.id = 'scroll-indicator';
                scrollIndicator.style.cssText = `
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: ${scrollProgress}%;
                    height: 3px;
                    background: linear-gradient(90deg, #667eea, #764ba2);
                    z-index: 1002;
                    transition: width 0.1s ease;
                `;
                document.body.appendChild(scrollIndicator);
            } else {
                document.getElementById('scroll-indicator').style.width = `${scrollProgress}%`;
            }
        });

        // 页面可见性检测
        document.addEventListener('visibilitychange', function() {
            if (document.hidden) {
                console.log('用户离开了页面');
            } else {
                console.log('用户回到了页面');
                showToast('欢迎回来！继续你的学习之旅吧！');
            }
        });
    </script>
</body>
</html>
