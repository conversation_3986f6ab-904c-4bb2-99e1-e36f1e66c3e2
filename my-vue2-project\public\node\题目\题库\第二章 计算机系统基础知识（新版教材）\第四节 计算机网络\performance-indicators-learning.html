<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>信息系统性能评价指标 - 互动学习</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/particles.js/2.0.0/particles.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            opacity: 0;
            transform: translateY(-50px);
        }

        .title {
            font-size: 3rem;
            color: white;
            margin-bottom: 20px;
            text-shadow: 0 4px 8px rgba(0,0,0,0.3);
        }

        .subtitle {
            font-size: 1.2rem;
            color: rgba(255,255,255,0.8);
            margin-bottom: 40px;
        }

        .progress-bar {
            width: 100%;
            height: 6px;
            background: rgba(255,255,255,0.2);
            border-radius: 3px;
            overflow: hidden;
            margin-bottom: 40px;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4facfe 0%, #00f2fe 100%);
            width: 0%;
            transition: width 0.8s ease;
        }

        .section {
            background: rgba(255,255,255,0.95);
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            opacity: 0;
            transform: translateY(50px);
        }

        .concept-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 30px;
            margin: 40px 0;
        }

        .concept-card {
            background: white;
            border-radius: 15px;
            padding: 30px;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            cursor: pointer;
            transition: all 0.3s ease;
            border: 3px solid transparent;
        }

        .concept-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.15);
        }

        .concept-card.active {
            border-color: #4facfe;
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
        }

        .concept-icon {
            width: 80px;
            height: 80px;
            margin: 0 auto 20px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            margin-bottom: 20px;
        }

        .canvas-container {
            width: 100%;
            height: 400px;
            background: #f8f9fa;
            border-radius: 15px;
            margin: 30px 0;
            position: relative;
            overflow: hidden;
        }

        #animationCanvas {
            width: 100%;
            height: 100%;
            border-radius: 15px;
        }

        .quiz-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-align: center;
        }

        .quiz-question {
            font-size: 1.5rem;
            margin-bottom: 30px;
            line-height: 1.6;
        }

        .options-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }

        .option-btn {
            background: rgba(255,255,255,0.1);
            border: 2px solid rgba(255,255,255,0.3);
            color: white;
            padding: 20px;
            border-radius: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 1.1rem;
        }

        .option-btn:hover {
            background: rgba(255,255,255,0.2);
            transform: translateY(-5px);
        }

        .option-btn.correct {
            background: #4caf50;
            border-color: #4caf50;
        }

        .option-btn.wrong {
            background: #f44336;
            border-color: #f44336;
        }

        .explanation {
            background: rgba(255,255,255,0.1);
            border-radius: 15px;
            padding: 30px;
            margin-top: 30px;
            text-align: left;
            display: none;
        }

        .btn {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1.1rem;
            transition: all 0.3s ease;
            margin: 10px;
        }

        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 20px rgba(79, 172, 254, 0.3);
        }

        .particles-js {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .pulse {
            animation: pulse 2s infinite;
        }
    </style>
</head>
<body>
    <div id="particles-js" class="particles-js"></div>
    
    <div class="container">
        <div class="header">
            <h1 class="title">信息系统性能评价指标</h1>
            <p class="subtitle">通过动画和互动，轻松掌握系统性能的核心概念</p>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
        </div>

        <!-- 概念介绍部分 -->
        <div class="section" id="conceptSection">
            <h2 style="text-align: center; margin-bottom: 30px; color: #333;">四大性能指标</h2>
            <div class="concept-grid">
                <div class="concept-card" data-concept="response-time">
                    <div class="concept-icon" style="background: linear-gradient(135deg, #ff6b6b, #ee5a24);">⏱️</div>
                    <h3>系统响应时间</h3>
                    <p>系统对请求作出响应的时间</p>
                </div>
                <div class="concept-card" data-concept="throughput">
                    <div class="concept-icon" style="background: linear-gradient(135deg, #4facfe, #00f2fe);">🚀</div>
                    <h3>吞吐量</h3>
                    <p>单位时间内处理请求的数量</p>
                </div>
                <div class="concept-card" data-concept="resource-usage">
                    <div class="concept-icon" style="background: linear-gradient(135deg, #a8edea, #fed6e3);">📊</div>
                    <h3>资源利用率</h3>
                    <p>系统资源被使用的时间比例</p>
                </div>
                <div class="concept-card" data-concept="concurrent-users">
                    <div class="concept-icon" style="background: linear-gradient(135deg, #ffecd2, #fcb69f);">👥</div>
                    <h3>并发用户数</h3>
                    <p>系统同时承载的用户数量</p>
                </div>
            </div>
        </div>

        <!-- 动画演示区域 -->
        <div class="section">
            <h2 style="text-align: center; margin-bottom: 30px; color: #333;">动画演示区</h2>
            <div class="canvas-container">
                <canvas id="animationCanvas"></canvas>
            </div>
            <div style="text-align: center;">
                <button class="btn" onclick="startAnimation('response-time')">响应时间演示</button>
                <button class="btn" onclick="startAnimation('throughput')">吞吐量演示</button>
                <button class="btn" onclick="startAnimation('resource-usage')">资源利用率演示</button>
                <button class="btn" onclick="startAnimation('concurrent-users')">并发用户演示</button>
            </div>
        </div>

        <!-- 题目练习部分 -->
        <div class="section quiz-section">
            <h2 style="margin-bottom: 30px;">题目练习</h2>
            <div class="quiz-question">
                信息系统的性能评价指标是客观评价信息系统性能的依据，其中，（ ）是指系统在单位时间内处理请求的数量。
            </div>
            <div class="options-grid">
                <button class="option-btn" data-option="A" onclick="selectOption('A')">
                    A. 系统响应时间
                </button>
                <button class="option-btn" data-option="B" onclick="selectOption('B')">
                    B. 吞吐量
                </button>
                <button class="option-btn" data-option="C" onclick="selectOption('C')">
                    C. 资源利用率
                </button>
                <button class="option-btn" data-option="D" onclick="selectOption('D')">
                    D. 并发用户数
                </button>
            </div>
            <div class="explanation" id="explanation">
                <h3>详细解析：</h3>
                <p><strong>正确答案：B. 吞吐量</strong></p>
                <ul style="text-align: left; margin: 20px 0;">
                    <li><strong>系统响应时间</strong>：指系统对请求作出响应的时间，单位通常是秒或毫秒</li>
                    <li><strong>吞吐量</strong>：指系统在单位时间内处理请求的数量，单位通常是请求/秒</li>
                    <li><strong>资源利用率</strong>：指各种部件被使用的时间与整个时间之比，用百分比表示</li>
                    <li><strong>并发用户数</strong>：指系统可以同时承载的正常使用系统功能的用户数量</li>
                </ul>
                <p><strong>记忆技巧</strong>：吞吐量就像工厂的生产线，关注的是单位时间能生产多少产品！</p>
            </div>
        </div>
    </div>

    <script>
        // 初始化粒子背景
        particlesJS('particles-js', {
            particles: {
                number: { value: 80, density: { enable: true, value_area: 800 } },
                color: { value: "#ffffff" },
                shape: { type: "circle" },
                opacity: { value: 0.5, random: false },
                size: { value: 3, random: true },
                line_linked: { enable: true, distance: 150, color: "#ffffff", opacity: 0.4, width: 1 },
                move: { enable: true, speed: 6, direction: "none", random: false, straight: false, out_mode: "out", bounce: false }
            },
            interactivity: {
                detect_on: "canvas",
                events: { onhover: { enable: true, mode: "repulse" }, onclick: { enable: true, mode: "push" }, resize: true },
                modes: { grab: { distance: 400, line_linked: { opacity: 1 } }, bubble: { distance: 400, size: 40, duration: 2, opacity: 8, speed: 3 }, repulse: { distance: 200, duration: 0.4 }, push: { particles_nb: 4 }, remove: { particles_nb: 2 } }
            },
            retina_detect: true
        });

        // 页面加载动画
        window.addEventListener('load', function() {
            gsap.to('.header', { duration: 1, opacity: 1, y: 0, ease: "power2.out" });
            gsap.to('.section', { duration: 1, opacity: 1, y: 0, stagger: 0.2, delay: 0.5, ease: "power2.out" });
        });

        // Canvas 动画
        const canvas = document.getElementById('animationCanvas');
        const ctx = canvas.getContext('2d');
        
        function resizeCanvas() {
            canvas.width = canvas.offsetWidth;
            canvas.height = canvas.offsetHeight;
        }
        resizeCanvas();
        window.addEventListener('resize', resizeCanvas);

        let animationId;
        let currentAnimation = null;

        // 概念卡片点击事件
        document.querySelectorAll('.concept-card').forEach(card => {
            card.addEventListener('click', function() {
                document.querySelectorAll('.concept-card').forEach(c => c.classList.remove('active'));
                this.classList.add('active');
                const concept = this.dataset.concept;
                startAnimation(concept);
                updateProgress(25);
            });
        });

        // 动画函数
        function startAnimation(type) {
            if (animationId) {
                cancelAnimationFrame(animationId);
            }
            
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            currentAnimation = type;
            
            switch(type) {
                case 'response-time':
                    animateResponseTime();
                    break;
                case 'throughput':
                    animateThroughput();
                    break;
                case 'resource-usage':
                    animateResourceUsage();
                    break;
                case 'concurrent-users':
                    animateConcurrentUsers();
                    break;
            }
        }

        function animateResponseTime() {
            let startTime = Date.now();
            let phase = 0; // 0: 发送请求, 1: 处理中, 2: 返回响应
            
            function draw() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                
                const elapsed = Date.now() - startTime;
                const cycleTime = 3000; // 3秒一个周期
                const progress = (elapsed % cycleTime) / cycleTime;
                
                // 绘制用户
                ctx.fillStyle = '#4facfe';
                ctx.beginPath();
                ctx.arc(100, canvas.height/2, 30, 0, Math.PI * 2);
                ctx.fill();
                ctx.fillStyle = 'white';
                ctx.font = '20px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('用户', 100, canvas.height/2 + 5);
                
                // 绘制服务器
                ctx.fillStyle = '#ff6b6b';
                ctx.fillRect(canvas.width - 150, canvas.height/2 - 40, 100, 80);
                ctx.fillStyle = 'white';
                ctx.fillText('服务器', canvas.width - 100, canvas.height/2 + 5);
                
                // 绘制请求和响应
                if (progress < 0.3) {
                    // 发送请求阶段
                    const requestX = 100 + (canvas.width - 250) * (progress / 0.3);
                    ctx.fillStyle = '#4facfe';
                    ctx.beginPath();
                    ctx.arc(requestX, canvas.height/2 - 20, 8, 0, Math.PI * 2);
                    ctx.fill();
                    ctx.fillStyle = 'black';
                    ctx.font = '14px Arial';
                    ctx.fillText('请求', requestX, canvas.height/2 - 30);
                } else if (progress < 0.7) {
                    // 处理阶段
                    ctx.fillStyle = '#ffa500';
                    ctx.font = '16px Arial';
                    ctx.textAlign = 'center';
                    ctx.fillText('处理中...', canvas.width - 100, canvas.height/2 - 60);
                    
                    // 绘制处理动画
                    const angle = (progress - 0.3) * 20 * Math.PI;
                    ctx.strokeStyle = '#ffa500';
                    ctx.lineWidth = 3;
                    ctx.beginPath();
                    ctx.arc(canvas.width - 100, canvas.height/2, 20, 0, angle);
                    ctx.stroke();
                } else {
                    // 返回响应阶段
                    const responseX = canvas.width - 150 - (canvas.width - 250) * ((progress - 0.7) / 0.3);
                    ctx.fillStyle = '#4caf50';
                    ctx.beginPath();
                    ctx.arc(responseX, canvas.height/2 + 20, 8, 0, Math.PI * 2);
                    ctx.fill();
                    ctx.fillStyle = 'black';
                    ctx.font = '14px Arial';
                    ctx.fillText('响应', responseX, canvas.height/2 + 35);
                }
                
                // 显示时间
                ctx.fillStyle = 'black';
                ctx.font = '18px Arial';
                ctx.textAlign = 'center';
                ctx.fillText(`响应时间: ${(progress * 3).toFixed(1)}秒`, canvas.width/2, 50);
                
                animationId = requestAnimationFrame(draw);
            }
            draw();
        }

        function animateThroughput() {
            let startTime = Date.now();
            let processedRequests = 0;
            
            function draw() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                
                const elapsed = Date.now() - startTime;
                const cycleTime = 5000; // 5秒一个周期
                const progress = (elapsed % cycleTime) / cycleTime;
                
                // 绘制服务器
                ctx.fillStyle = '#4facfe';
                ctx.fillRect(canvas.width/2 - 60, canvas.height/2 - 40, 120, 80);
                ctx.fillStyle = 'white';
                ctx.font = '16px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('服务器', canvas.width/2, canvas.height/2 + 5);
                
                // 绘制请求队列
                const requestsPerSecond = 10;
                const totalRequests = Math.floor(progress * requestsPerSecond * 5);
                
                for (let i = 0; i < Math.min(totalRequests, 20); i++) {
                    const x = 50 + (i % 10) * 40;
                    const y = 100 + Math.floor(i / 10) * 40;
                    
                    if (i < processedRequests) {
                        ctx.fillStyle = '#4caf50'; // 已处理
                    } else {
                        ctx.fillStyle = '#ffa500'; // 等待处理
                    }
                    
                    ctx.beginPath();
                    ctx.arc(x, y, 15, 0, Math.PI * 2);
                    ctx.fill();
                    ctx.fillStyle = 'white';
                    ctx.font = '12px Arial';
                    ctx.fillText(i + 1, x, y + 4);
                }
                
                // 更新处理数量
                if (elapsed % 500 < 50) { // 每0.5秒处理一批
                    processedRequests = Math.min(processedRequests + 2, totalRequests);
                }
                
                // 显示吞吐量
                ctx.fillStyle = 'black';
                ctx.font = '20px Arial';
                ctx.textAlign = 'center';
                ctx.fillText(`吞吐量: ${Math.floor(processedRequests / (elapsed / 1000))} 请求/秒`, canvas.width/2, canvas.height - 50);
                ctx.fillText(`已处理: ${processedRequests} / ${totalRequests}`, canvas.width/2, canvas.height - 20);
                
                animationId = requestAnimationFrame(draw);
            }
            draw();
        }

        function animateResourceUsage() {
            let startTime = Date.now();
            
            function draw() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                
                const elapsed = Date.now() - startTime;
                const cycleTime = 4000;
                const progress = (elapsed % cycleTime) / cycleTime;
                
                // 绘制资源组件
                const resources = [
                    { name: 'CPU', color: '#ff6b6b', usage: 30 + 40 * Math.sin(progress * Math.PI * 2) },
                    { name: '内存', color: '#4facfe', usage: 50 + 30 * Math.sin(progress * Math.PI * 2 + 1) },
                    { name: '磁盘', color: '#4caf50', usage: 20 + 25 * Math.sin(progress * Math.PI * 2 + 2) },
                    { name: '网络', color: '#ffa500', usage: 40 + 35 * Math.sin(progress * Math.PI * 2 + 3) }
                ];
                
                resources.forEach((resource, index) => {
                    const x = 100 + index * 200;
                    const y = canvas.height/2;
                    const barHeight = 200;
                    const usageHeight = (resource.usage / 100) * barHeight;
                    
                    // 绘制背景条
                    ctx.fillStyle = '#e0e0e0';
                    ctx.fillRect(x - 30, y - barHeight/2, 60, barHeight);
                    
                    // 绘制使用量条
                    ctx.fillStyle = resource.color;
                    ctx.fillRect(x - 30, y + barHeight/2 - usageHeight, 60, usageHeight);
                    
                    // 绘制标签
                    ctx.fillStyle = 'black';
                    ctx.font = '16px Arial';
                    ctx.textAlign = 'center';
                    ctx.fillText(resource.name, x, y + barHeight/2 + 30);
                    ctx.fillText(`${resource.usage.toFixed(1)}%`, x, y + barHeight/2 + 50);
                });
                
                // 标题
                ctx.fillStyle = 'black';
                ctx.font = '20px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('系统资源利用率实时监控', canvas.width/2, 50);
                
                animationId = requestAnimationFrame(draw);
            }
            draw();
        }

        function animateConcurrentUsers() {
            let startTime = Date.now();
            
            function draw() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                
                const elapsed = Date.now() - startTime;
                const cycleTime = 6000;
                const progress = (elapsed % cycleTime) / cycleTime;
                
                // 计算当前用户数
                const maxUsers = 50;
                const currentUsers = Math.floor(maxUsers * (0.3 + 0.7 * Math.sin(progress * Math.PI * 2)));
                
                // 绘制服务器
                ctx.fillStyle = '#4facfe';
                ctx.fillRect(canvas.width/2 - 80, canvas.height/2 - 60, 160, 120);
                ctx.fillStyle = 'white';
                ctx.font = '18px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('服务器', canvas.width/2, canvas.height/2);
                ctx.fillText(`${currentUsers}/${maxUsers}`, canvas.width/2, canvas.height/2 + 25);
                
                // 绘制用户
                const radius = 150;
                for (let i = 0; i < currentUsers; i++) {
                    const angle = (i / maxUsers) * Math.PI * 2;
                    const x = canvas.width/2 + Math.cos(angle) * radius;
                    const y = canvas.height/2 + Math.sin(angle) * radius;
                    
                    // 用户图标
                    ctx.fillStyle = i < currentUsers * 0.8 ? '#4caf50' : '#ffa500';
                    ctx.beginPath();
                    ctx.arc(x, y, 8, 0, Math.PI * 2);
                    ctx.fill();
                    
                    // 连接线
                    ctx.strokeStyle = 'rgba(79, 172, 254, 0.3)';
                    ctx.lineWidth = 1;
                    ctx.beginPath();
                    ctx.moveTo(x, y);
                    ctx.lineTo(canvas.width/2, canvas.height/2);
                    ctx.stroke();
                }
                
                // 状态指示
                ctx.fillStyle = 'black';
                ctx.font = '16px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('并发用户连接状态', canvas.width/2, 50);
                
                // 图例
                ctx.fillStyle = '#4caf50';
                ctx.beginPath();
                ctx.arc(100, canvas.height - 50, 8, 0, Math.PI * 2);
                ctx.fill();
                ctx.fillStyle = 'black';
                ctx.textAlign = 'left';
                ctx.fillText('正常连接', 120, canvas.height - 45);
                
                ctx.fillStyle = '#ffa500';
                ctx.beginPath();
                ctx.arc(250, canvas.height - 50, 8, 0, Math.PI * 2);
                ctx.fill();
                ctx.fillText('高负载', 270, canvas.height - 45);
                
                animationId = requestAnimationFrame(draw);
            }
            draw();
        }

        // 题目选择
        function selectOption(option) {
            const buttons = document.querySelectorAll('.option-btn');
            buttons.forEach(btn => {
                btn.classList.remove('correct', 'wrong');
                if (btn.dataset.option === 'B') {
                    btn.classList.add('correct');
                } else if (btn.dataset.option === option && option !== 'B') {
                    btn.classList.add('wrong');
                }
            });
            
            document.getElementById('explanation').style.display = 'block';
            updateProgress(100);
            
            // 添加庆祝动画
            if (option === 'B') {
                createCelebration();
            }
        }

        // 庆祝动画
        function createCelebration() {
            for (let i = 0; i < 50; i++) {
                setTimeout(() => {
                    const confetti = document.createElement('div');
                    confetti.style.position = 'fixed';
                    confetti.style.left = Math.random() * 100 + 'vw';
                    confetti.style.top = '-10px';
                    confetti.style.width = '10px';
                    confetti.style.height = '10px';
                    confetti.style.background = ['#ff6b6b', '#4facfe', '#4caf50', '#ffa500'][Math.floor(Math.random() * 4)];
                    confetti.style.borderRadius = '50%';
                    confetti.style.zIndex = '1000';
                    confetti.style.pointerEvents = 'none';
                    document.body.appendChild(confetti);
                    
                    gsap.to(confetti, {
                        duration: 3,
                        y: window.innerHeight + 100,
                        rotation: 360,
                        ease: "power2.out",
                        onComplete: () => confetti.remove()
                    });
                }, i * 50);
            }
        }

        // 更新进度条
        function updateProgress(percent) {
            document.getElementById('progressFill').style.width = percent + '%';
        }

        // 自动开始第一个动画
        setTimeout(() => {
            startAnimation('throughput');
        }, 2000);
    </script>
</body>
</html>
