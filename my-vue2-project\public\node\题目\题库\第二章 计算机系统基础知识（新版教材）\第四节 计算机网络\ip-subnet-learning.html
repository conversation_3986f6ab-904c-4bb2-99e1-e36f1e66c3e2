<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>IP地址与子网掩码 - 交互式学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            opacity: 0;
            animation: fadeInUp 1s ease-out forwards;
        }

        .title {
            font-size: 3rem;
            font-weight: 700;
            color: white;
            margin-bottom: 20px;
            text-shadow: 0 4px 20px rgba(0,0,0,0.3);
        }

        .subtitle {
            font-size: 1.2rem;
            color: rgba(255,255,255,0.9);
            font-weight: 300;
        }

        .learning-section {
            background: white;
            border-radius: 24px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.1);
            opacity: 0;
            transform: translateY(30px);
            animation: fadeInUp 1s ease-out 0.3s forwards;
        }

        .section-title {
            font-size: 2rem;
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 30px;
            text-align: center;
        }

        .ip-display {
            display: flex;
            justify-content: center;
            align-items: center;
            margin: 40px 0;
            flex-wrap: wrap;
            gap: 20px;
        }

        .ip-part {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 20px 30px;
            border-radius: 16px;
            font-size: 1.8rem;
            font-weight: 600;
            min-width: 120px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 8px 25px rgba(79, 172, 254, 0.3);
        }

        .ip-part:hover {
            transform: translateY(-5px) scale(1.05);
            box-shadow: 0 15px 35px rgba(79, 172, 254, 0.4);
        }

        .ip-part.network {
            background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
            box-shadow: 0 8px 25px rgba(250, 112, 154, 0.3);
        }

        .ip-part.subnet {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
            color: #333;
            box-shadow: 0 8px 25px rgba(168, 237, 234, 0.3);
        }

        .ip-part.host {
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            color: #333;
            box-shadow: 0 8px 25px rgba(252, 182, 159, 0.3);
        }

        .dot {
            font-size: 2rem;
            font-weight: bold;
            color: #666;
            animation: pulse 2s infinite;
        }

        .explanation {
            background: #f8fafc;
            border-radius: 16px;
            padding: 30px;
            margin: 30px 0;
            border-left: 6px solid #4facfe;
        }

        .step {
            margin: 20px 0;
            padding: 20px;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.05);
            opacity: 0;
            transform: translateX(-30px);
            transition: all 0.5s ease;
        }

        .step.active {
            opacity: 1;
            transform: translateX(0);
        }

        .step-number {
            display: inline-block;
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 50%;
            text-align: center;
            line-height: 40px;
            font-weight: bold;
            margin-right: 15px;
        }

        .interactive-demo {
            text-align: center;
            margin: 40px 0;
        }

        .demo-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 50px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }

        .demo-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 35px rgba(102, 126, 234, 0.4);
        }

        .canvas-container {
            display: flex;
            justify-content: center;
            margin: 40px 0;
        }

        canvas {
            border-radius: 16px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .quiz-section {
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            border-radius: 24px;
            padding: 40px;
            margin-top: 40px;
            opacity: 0;
            animation: fadeInUp 1s ease-out 1s forwards;
        }

        .quiz-title {
            font-size: 2rem;
            font-weight: 600;
            color: #2d3748;
            text-align: center;
            margin-bottom: 30px;
        }

        .question {
            background: white;
            border-radius: 16px;
            padding: 30px;
            margin-bottom: 20px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }

        .options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }

        .option {
            background: #f8fafc;
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            padding: 15px 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
            font-weight: 500;
        }

        .option:hover {
            border-color: #4facfe;
            background: #ebf8ff;
            transform: translateY(-2px);
        }

        .option.correct {
            background: #c6f6d5;
            border-color: #38a169;
            color: #22543d;
        }

        .option.wrong {
            background: #fed7d7;
            border-color: #e53e3e;
            color: #742a2a;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
            40% { transform: translateY(-10px); }
            60% { transform: translateY(-5px); }
        }

        .highlight {
            animation: bounce 1s ease-in-out;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">IP地址与子网掩码</h1>
            <p class="subtitle">零基础交互式学习 - 让网络概念变得简单有趣</p>
        </div>

        <div class="learning-section">
            <h2 class="section-title">📚 基础知识：什么是IP地址？</h2>
            <div class="explanation">
                <p style="font-size: 1.2rem; line-height: 1.8; margin-bottom: 20px;">
                    IP地址就像你家的门牌号码，让网络中的设备能够找到彼此。每个IP地址由4个数字组成，用点分隔。
                </p>
                <div class="ip-display">
                    <div class="ip-part network" onclick="highlightPart(this)">140</div>
                    <div class="dot">.</div>
                    <div class="ip-part network" onclick="highlightPart(this)">252</div>
                    <div class="dot">.</div>
                    <div class="ip-part subnet" onclick="highlightPart(this)">12</div>
                    <div class="dot">.</div>
                    <div class="ip-part host" onclick="highlightPart(this)">120</div>
                </div>
                <p style="text-align: center; margin-top: 20px; font-style: italic; color: #666;">
                    点击每个数字部分，看看它们的作用！
                </p>
            </div>
        </div>

        <div class="learning-section">
            <h2 class="section-title">🔍 解题步骤演示</h2>
            <div class="interactive-demo">
                <button class="demo-button" onclick="startDemo()">开始动画演示</button>
            </div>
            
            <div id="steps-container">
                <div class="step" id="step1">
                    <span class="step-number">1</span>
                    <strong>识别IP地址类型：</strong>14************ 是B类地址（第一个数字在128-191之间）
                </div>
                <div class="step" id="step2">
                    <span class="step-number">2</span>
                    <strong>分析子网掩码：</strong>************* 表示前24位是网络位，后8位是主机位
                </div>
                <div class="step" id="step3">
                    <span class="step-number">3</span>
                    <strong>确定网络部分：</strong>140.252 是原始网络号
                </div>
                <div class="step" id="step4">
                    <span class="step-number">4</span>
                    <strong>确定子网部分：</strong>12 是子网号
                </div>
                <div class="step" id="step5">
                    <span class="step-number">5</span>
                    <strong>确定主机部分：</strong>120 是主机号，答案是 *********
                </div>
            </div>

            <div class="canvas-container">
                <canvas id="demoCanvas" width="800" height="400"></canvas>
            </div>
        </div>

        <div class="quiz-section">
            <h2 class="quiz-title">🎯 练习题目</h2>
            <div class="question">
                <h3 style="margin-bottom: 20px;">给定IP地址为14************，子网掩码是*************，那么主机号是？</h3>
                <div class="options">
                    <div class="option" onclick="selectOption(this, true)">A. *********</div>
                    <div class="option" onclick="selectOption(this, false)">B. **********</div>
                    <div class="option" onclick="selectOption(this, false)">C. ********</div>
                    <div class="option" onclick="selectOption(this, false)">D. ************</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let currentStep = 0;
        let canvas, ctx;

        function highlightPart(element) {
            element.classList.add('highlight');
            setTimeout(() => element.classList.remove('highlight'), 1000);
            
            const text = element.textContent;
            if (element.classList.contains('network')) {
                showTooltip('这是网络部分，标识网络');
            } else if (element.classList.contains('subnet')) {
                showTooltip('这是子网部分，用于划分子网');
            } else if (element.classList.contains('host')) {
                showTooltip('这是主机部分，标识具体设备');
            }
        }

        function showTooltip(message) {
            // 简单的提示实现
            alert(message);
        }

        function startDemo() {
            currentStep = 0;
            showNextStep();
        }

        function showNextStep() {
            if (currentStep < 5) {
                const step = document.getElementById(`step${currentStep + 1}`);
                step.classList.add('active');
                currentStep++;
                
                // 绘制对应的动画
                drawStepAnimation(currentStep);
                
                setTimeout(showNextStep, 2000);
            }
        }

        function drawStepAnimation(step) {
            if (!canvas) {
                canvas = document.getElementById('demoCanvas');
                ctx = canvas.getContext('2d');
            }
            
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 绘制IP地址框
            const parts = ['140', '252', '12', '120'];
            const colors = ['#fa709a', '#fa709a', '#a8edea', '#ffecd2'];
            const labels = ['网络', '网络', '子网', '主机'];
            
            for (let i = 0; i < 4; i++) {
                const x = 100 + i * 150;
                const y = 150;
                
                // 绘制方框
                ctx.fillStyle = colors[i];
                ctx.fillRect(x, y, 120, 60);
                ctx.strokeStyle = '#333';
                ctx.lineWidth = 2;
                ctx.strokeRect(x, y, 120, 60);
                
                // 绘制数字
                ctx.fillStyle = i < 2 ? 'white' : '#333';
                ctx.font = 'bold 24px Arial';
                ctx.textAlign = 'center';
                ctx.fillText(parts[i], x + 60, y + 40);
                
                // 绘制标签
                ctx.fillStyle = '#666';
                ctx.font = '16px Arial';
                ctx.fillText(labels[i], x + 60, y + 85);
                
                // 根据当前步骤高亮
                if (step === 3 && i < 2) {
                    ctx.strokeStyle = '#ff6b6b';
                    ctx.lineWidth = 4;
                    ctx.strokeRect(x - 2, y - 2, 124, 64);
                } else if (step === 4 && i === 2) {
                    ctx.strokeStyle = '#4ecdc4';
                    ctx.lineWidth = 4;
                    ctx.strokeRect(x - 2, y - 2, 124, 64);
                } else if (step === 5 && i === 3) {
                    ctx.strokeStyle = '#45b7d1';
                    ctx.lineWidth = 4;
                    ctx.strokeRect(x - 2, y - 2, 124, 64);
                }
            }
            
            // 绘制点分隔符
            ctx.fillStyle = '#333';
            ctx.font = 'bold 30px Arial';
            for (let i = 0; i < 3; i++) {
                ctx.fillText('•', 220 + i * 150, 185);
            }
            
            // 绘制子网掩码
            ctx.fillStyle = '#666';
            ctx.font = '18px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('子网掩码: *************', 400, 280);
            
            // 绘制二进制表示
            if (step >= 2) {
                ctx.fillStyle = '#999';
                ctx.font = '14px Arial';
                ctx.fillText('11111111.11111111.11111111.00000000', 400, 300);
                ctx.fillText('网络位 ←→ 主机位', 400, 320);
            }
        }

        function selectOption(element, isCorrect) {
            // 清除之前的选择
            document.querySelectorAll('.option').forEach(opt => {
                opt.classList.remove('correct', 'wrong');
            });
            
            if (isCorrect) {
                element.classList.add('correct');
                setTimeout(() => {
                    alert('🎉 恭喜答对了！\n\n解析：\n• IP地址 14************\n• 子网掩码 ************* 表示前24位是网络+子网位\n• 最后8位(120)是主机位\n• 所以主机号是 *********');
                }, 500);
            } else {
                element.classList.add('wrong');
                setTimeout(() => {
                    alert('❌ 答案不正确，再想想看！\n\n提示：主机号只包含IP地址中对应主机位的部分。');
                }, 500);
            }
        }

        // 初始化
        window.onload = function() {
            canvas = document.getElementById('demoCanvas');
            ctx = canvas.getContext('2d');
            drawStepAnimation(0);
        };
    </script>
</body>
</html>
