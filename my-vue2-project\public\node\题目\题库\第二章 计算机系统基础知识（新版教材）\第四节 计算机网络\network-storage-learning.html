<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>网络存储技术 - 交互式学习</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .section {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 40px;
            margin: 30px 0;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            opacity: 0;
            transform: translateY(50px);
        }

        .section.active {
            opacity: 1;
            transform: translateY(0);
        }

        h1 {
            font-size: 2.5em;
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        h2 {
            font-size: 2em;
            color: #34495e;
            margin-bottom: 20px;
            border-left: 5px solid #667eea;
            padding-left: 15px;
        }

        .question-box {
            background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
            padding: 30px;
            border-radius: 15px;
            margin: 20px 0;
            font-size: 1.2em;
            line-height: 1.6;
        }

        .options {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin: 20px 0;
        }

        .option {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 10px;
            border: 2px solid transparent;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 1.1em;
        }

        .option:hover {
            background: #e9ecef;
            transform: translateY(-2px);
        }

        .option.correct {
            border-color: #28a745;
            background: #d4edda;
        }

        .option.wrong {
            border-color: #dc3545;
            background: #f8d7da;
        }

        .canvas-container {
            display: flex;
            justify-content: center;
            margin: 30px 0;
            background: #f8f9fa;
            border-radius: 15px;
            padding: 20px;
        }

        canvas {
            border: 2px solid #dee2e6;
            border-radius: 10px;
            background: white;
        }

        .interactive-demo {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
            padding: 30px;
            border-radius: 15px;
            margin: 20px 0;
        }

        .storage-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            cursor: pointer;
            transition: all 0.3s ease;
            border: 3px solid transparent;
        }

        .storage-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
        }

        .storage-card.selected {
            border-color: #667eea;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .progress-bar {
            width: 100%;
            height: 10px;
            background: #e9ecef;
            border-radius: 5px;
            overflow: hidden;
            margin: 20px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            width: 0%;
            transition: width 0.5s ease;
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1.1em;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }

        .explanation {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            font-size: 1.1em;
            line-height: 1.6;
        }

        .comparison-container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }

        .comparison-item {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .comparison-item.correct {
            border-left: 5px solid #28a745;
        }

        .comparison-item.wrong {
            border-left: 5px solid #dc3545;
        }

        .navigation {
            position: fixed;
            bottom: 30px;
            right: 30px;
            display: flex;
            gap: 10px;
        }

        .nav-btn {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            font-size: 1.2em;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .nav-btn:hover {
            transform: scale(1.1);
        }

        .hidden {
            display: none;
        }

        .drag-items {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-bottom: 20px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
        }

        .drag-item {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 10px 15px;
            border-radius: 20px;
            cursor: grab;
            user-select: none;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }

        .drag-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
        }

        .drag-item:active {
            cursor: grabbing;
        }

        .drop-zones {
            display: flex;
            gap: 20px;
            justify-content: space-around;
        }

        .drop-zone {
            flex: 1;
            text-align: center;
        }

        .drop-zone h4 {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 10px;
            border-radius: 10px 10px 0 0;
            margin: 0;
        }

        .drop-area {
            min-height: 150px;
            border: 3px dashed #dee2e6;
            border-radius: 0 0 10px 10px;
            padding: 20px;
            display: flex;
            flex-direction: column;
            gap: 10px;
            transition: all 0.3s ease;
        }

        .drop-area:hover {
            border-color: #667eea;
            background: rgba(102, 126, 234, 0.1);
        }

        .firework {
            pointer-events: none;
            z-index: 1000;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        .storage-card.selected {
            animation: pulse 2s infinite;
        }

        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }

            .section {
                padding: 20px;
            }

            .options {
                grid-template-columns: 1fr;
            }

            .comparison-container {
                grid-template-columns: 1fr;
            }

            .drop-zones {
                flex-direction: column;
            }

            canvas {
                width: 100%;
                height: auto;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 第一部分：题目展示 -->
        <div class="section active" id="section-1">
            <h1>网络存储技术学习</h1>
            <div class="question-box">
                <h3>📝 考试题目</h3>
                <p><strong>以下关于网络存储的叙述，正确的是（ ）。</strong></p>
                <div class="options">
                    <div class="option" data-option="A">A. DAS支持完全跨平台文件共享，支持所有的操作系统</div>
                    <div class="option" data-option="B">B. NAS通过SCSI连接至服务器，通过服务器网卡在网络上传输数据</div>
                    <div class="option" data-option="C">C. FC SAN的网络介质为光纤通道，而IP SAN使用标准的以太网</div>
                    <div class="option" data-option="D">D. SAN设备有自己的文件管理系统，NAS中的存储设备没有文件管理系统</div>
                </div>
                <div class="explanation">
                    <strong>正确答案：C</strong><br>
                    让我们从零开始学习网络存储技术，理解为什么C是正确答案！
                </div>
            </div>
            <div class="progress-bar">
                <div class="progress-fill" id="progress"></div>
            </div>
            <button class="btn" onclick="nextSection()">开始学习 →</button>
        </div>

        <!-- 第二部分：基础概念介绍 -->
        <div class="section" id="section-2">
            <h2>🏗️ 什么是网络存储？</h2>
            <div class="canvas-container">
                <canvas id="introCanvas" width="800" height="400"></canvas>
            </div>
            <div class="explanation">
                <p>网络存储是指通过网络连接的存储设备，让多台计算机可以共享存储资源。</p>
                <p>就像一个大型的"网络硬盘"，大家都可以通过网络来存取文件。</p>
            </div>
            <button class="btn" onclick="startIntroAnimation()">观看动画演示</button>
            <button class="btn" onclick="nextSection()">继续学习 →</button>
        </div>

        <!-- 第三部分：DAS详解 -->
        <div class="section" id="section-3">
            <h2>💾 DAS - 直接附加存储</h2>
            <div class="canvas-container">
                <canvas id="dasCanvas" width="800" height="400"></canvas>
            </div>
            <div class="interactive-demo">
                <h3>🎮 互动体验：DAS连接方式</h3>
                <p>点击下面的组件，看看DAS是如何工作的：</p>
                <div style="display: flex; justify-content: space-around; margin: 20px 0;">
                    <div class="storage-card" onclick="showDASConnection('server')">
                        <h4>🖥️ 服务器</h4>
                        <p>处理数据请求</p>
                    </div>
                    <div class="storage-card" onclick="showDASConnection('cable')">
                        <h4>🔌 SCSI电缆</h4>
                        <p>直接连接</p>
                    </div>
                    <div class="storage-card" onclick="showDASConnection('storage')">
                        <h4>💿 存储设备</h4>
                        <p>硬盘阵列</p>
                    </div>
                </div>
            </div>
            <div class="explanation">
                <strong>DAS特点：</strong>
                <ul>
                    <li>✅ 直接通过SCSI电缆连接服务器</li>
                    <li>❌ 不支持跨平台文件共享</li>
                    <li>❌ 依赖于特定服务器</li>
                    <li>❌ 不能独立工作</li>
                </ul>
            </div>
            <button class="btn" onclick="nextSection()">学习NAS →</button>
        </div>

        <!-- 第四部分：NAS详解 -->
        <div class="section" id="section-4">
            <h2>🌐 NAS - 网络附加存储</h2>
            <div class="canvas-container">
                <canvas id="nasCanvas" width="800" height="400"></canvas>
            </div>
            <div class="interactive-demo">
                <h3>🎮 互动对比：NAS vs DAS</h3>
                <div class="comparison-container">
                    <div class="comparison-item wrong">
                        <h4>❌ 错误理解</h4>
                        <p><strong>选项B说：</strong>NAS通过SCSI连接至服务器</p>
                        <p>这是错误的！NAS是直接连接到网络的。</p>
                    </div>
                    <div class="comparison-item correct">
                        <h4>✅ 正确理解</h4>
                        <p><strong>实际上：</strong>NAS直接连接到网络</p>
                        <p>NAS有自己的操作系统，可以独立工作。</p>
                    </div>
                </div>
                <button class="btn" onclick="animateNASConnection()">观看NAS连接动画</button>
            </div>
            <div class="explanation">
                <strong>NAS特点：</strong>
                <ul>
                    <li>✅ 直接连接到网络，不依赖特定服务器</li>
                    <li>✅ 有自己的操作系统（瘦服务器）</li>
                    <li>✅ 支持跨平台文件共享</li>
                    <li>✅ 可以独立工作</li>
                </ul>
            </div>
            <button class="btn" onclick="nextSection()">学习SAN →</button>
        </div>

        <!-- 第五部分：SAN详解 -->
        <div class="section" id="section-5">
            <h2>⚡ SAN - 存储区域网络</h2>
            <div class="canvas-container">
                <canvas id="sanCanvas" width="800" height="400"></canvas>
            </div>
            <div class="interactive-demo">
                <h3>🎮 互动学习：SAN的两种类型</h3>
                <div style="display: flex; justify-content: space-around; margin: 20px 0;">
                    <div class="storage-card" onclick="showSANType('fc')">
                        <h4>🔥 FC SAN</h4>
                        <p>光纤通道</p>
                        <p>高速专用网络</p>
                    </div>
                    <div class="storage-card" onclick="showSANType('ip')">
                        <h4>🌐 IP SAN</h4>
                        <p>以太网</p>
                        <p>标准网络协议</p>
                    </div>
                </div>
                <div id="san-explanation" class="explanation">
                    点击上面的卡片了解不同类型的SAN
                </div>
            </div>
            <button class="btn" onclick="nextSection()">错误分析 →</button>
        </div>

        <!-- 第六部分：错误选项分析 -->
        <div class="section" id="section-6">
            <h2>🔍 错误选项深度分析</h2>
            <div class="interactive-demo">
                <h3>🎯 逐一击破错误选项</h3>
                <div class="options">
                    <div class="option wrong" onclick="explainWrongOption('A')">
                        <strong>选项A - 错误</strong><br>
                        DAS支持完全跨平台文件共享
                    </div>
                    <div class="option wrong" onclick="explainWrongOption('B')">
                        <strong>选项B - 错误</strong><br>
                        NAS通过SCSI连接至服务器
                    </div>
                    <div class="option correct">
                        <strong>选项C - 正确 ✅</strong><br>
                        FC SAN用光纤，IP SAN用以太网
                    </div>
                    <div class="option wrong" onclick="explainWrongOption('D')">
                        <strong>选项D - 错误</strong><br>
                        SAN有文件系统，NAS没有
                    </div>
                </div>
                <div id="wrong-explanation" class="explanation">
                    点击错误选项查看详细解释
                </div>
            </div>
            <button class="btn" onclick="nextSection()">总结对比 →</button>
        </div>

        <!-- 第七部分：综合对比 -->
        <div class="section" id="section-7">
            <h2>📊 三种存储技术对比</h2>
            <div class="canvas-container">
                <canvas id="comparisonCanvas" width="800" height="500"></canvas>
            </div>
            <div class="interactive-demo">
                <h3>🎮 拖拽匹配游戏</h3>
                <p>将特征拖拽到对应的存储技术上：</p>
                <div id="drag-game" style="margin: 20px 0;">
                    <div class="drag-items">
                        <div class="drag-item" draggable="true" data-answer="das">直接SCSI连接</div>
                        <div class="drag-item" draggable="true" data-answer="nas">有自己的OS</div>
                        <div class="drag-item" draggable="true" data-answer="san">光纤通道或以太网</div>
                        <div class="drag-item" draggable="true" data-answer="das">不支持跨平台</div>
                        <div class="drag-item" draggable="true" data-answer="nas">网络文件共享</div>
                        <div class="drag-item" draggable="true" data-answer="san">块级存储</div>
                    </div>
                    <div class="drop-zones">
                        <div class="drop-zone" data-type="das">
                            <h4>DAS</h4>
                            <div class="drop-area"></div>
                        </div>
                        <div class="drop-zone" data-type="nas">
                            <h4>NAS</h4>
                            <div class="drop-area"></div>
                        </div>
                        <div class="drop-zone" data-type="san">
                            <h4>SAN</h4>
                            <div class="drop-area"></div>
                        </div>
                    </div>
                </div>
            </div>
            <button class="btn" onclick="nextSection()">最终测试 →</button>
        </div>

        <!-- 第八部分：最终测试 -->
        <div class="section" id="section-8">
            <h2>🎓 最终理解测试</h2>
            <div class="question-box">
                <h3>🧠 现在你能正确回答了吗？</h3>
                <p><strong>以下关于网络存储的叙述，正确的是（ ）。</strong></p>
                <div class="options">
                    <div class="option" data-final="A">A. DAS支持完全跨平台文件共享，支持所有的操作系统</div>
                    <div class="option" data-final="B">B. NAS通过SCSI连接至服务器，通过服务器网卡在网络上传输数据</div>
                    <div class="option" data-final="C">C. FC SAN的网络介质为光纤通道，而IP SAN使用标准的以太网</div>
                    <div class="option" data-final="D">D. SAN设备有自己的文件管理系统，NAS中的存储设备没有文件管理系统</div>
                </div>
            </div>
            <div id="final-result" class="explanation hidden">
                <h3>🎉 恭喜你完成学习！</h3>
                <p>通过这次学习，你已经掌握了网络存储的核心概念。记住关键点：</p>
                <ul>
                    <li><strong>DAS：</strong>直接连接，不支持跨平台共享</li>
                    <li><strong>NAS：</strong>网络连接，有自己的OS，支持文件共享</li>
                    <li><strong>SAN：</strong>专用存储网络，FC用光纤，IP用以太网</li>
                </ul>
            </div>
            <button class="btn" onclick="restartLearning()">重新学习</button>
        </div>
    </div>

    <div class="navigation">
        <button class="nav-btn" onclick="prevSection()">←</button>
        <button class="nav-btn" onclick="nextSection()">→</button>
    </div>

    <script>
        let currentSection = 1;
        const totalSections = 8;

        // 初始化Canvas
        function initCanvas() {
            const introCanvas = document.getElementById('introCanvas');
            const dasCanvas = document.getElementById('dasCanvas');
            const nasCanvas = document.getElementById('nasCanvas');
            const sanCanvas = document.getElementById('sanCanvas');
            const comparisonCanvas = document.getElementById('comparisonCanvas');

            if (introCanvas) {
                const ctx = introCanvas.getContext('2d');
                drawNetworkOverview(ctx);
            }

            if (dasCanvas) {
                const ctx = dasCanvas.getContext('2d');
                drawDASArchitecture(ctx);
            }

            if (nasCanvas) {
                const ctx = nasCanvas.getContext('2d');
                drawNASArchitecture(ctx);
            }

            if (sanCanvas) {
                const ctx = sanCanvas.getContext('2d');
                drawSANArchitecture(ctx);
            }

            if (comparisonCanvas) {
                const ctx = comparisonCanvas.getContext('2d');
                drawComparisonChart(ctx);
            }
        }

        // 绘制网络存储概览
        function drawNetworkOverview(ctx) {
            ctx.clearRect(0, 0, 800, 400);
            
            // 绘制网络云
            ctx.fillStyle = '#667eea';
            ctx.beginPath();
            ctx.arc(400, 200, 80, 0, Math.PI * 2);
            ctx.fill();
            
            ctx.fillStyle = 'white';
            ctx.font = '16px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('网络', 400, 205);
            
            // 绘制计算机
            const computers = [
                {x: 200, y: 100, label: '计算机A'},
                {x: 600, y: 100, label: '计算机B'},
                {x: 200, y: 300, label: '计算机C'},
                {x: 600, y: 300, label: '存储设备'}
            ];
            
            computers.forEach(comp => {
                ctx.fillStyle = '#34495e';
                ctx.fillRect(comp.x - 30, comp.y - 20, 60, 40);
                ctx.fillStyle = 'white';
                ctx.fillText(comp.label, comp.x, comp.y + 5);
                
                // 绘制连接线
                ctx.strokeStyle = '#667eea';
                ctx.lineWidth = 3;
                ctx.beginPath();
                ctx.moveTo(comp.x, comp.y);
                ctx.lineTo(400, 200);
                ctx.stroke();
            });
        }

        // 绘制DAS架构
        function drawDASArchitecture(ctx) {
            ctx.clearRect(0, 0, 800, 400);

            // 服务器
            ctx.fillStyle = '#3498db';
            ctx.fillRect(150, 150, 100, 100);
            ctx.fillStyle = 'white';
            ctx.font = '14px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('服务器', 200, 205);

            // SCSI电缆
            ctx.strokeStyle = '#e74c3c';
            ctx.lineWidth = 8;
            ctx.beginPath();
            ctx.moveTo(250, 200);
            ctx.lineTo(450, 200);
            ctx.stroke();

            ctx.fillStyle = '#e74c3c';
            ctx.fillText('SCSI电缆', 350, 190);

            // 存储设备
            ctx.fillStyle = '#2ecc71';
            ctx.fillRect(450, 150, 100, 100);
            ctx.fillStyle = 'white';
            ctx.fillText('存储设备', 500, 205);

            // 添加说明文字
            ctx.fillStyle = '#34495e';
            ctx.font = '12px Microsoft YaHei';
            ctx.fillText('直接连接，无法跨平台共享', 350, 280);
        }

        // 绘制NAS架构
        function drawNASArchitecture(ctx) {
            ctx.clearRect(0, 0, 800, 400);

            // 网络交换机
            ctx.fillStyle = '#667eea';
            ctx.fillRect(350, 180, 100, 40);
            ctx.fillStyle = 'white';
            ctx.font = '14px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('网络交换机', 400, 205);

            // NAS设备
            ctx.fillStyle = '#f39c12';
            ctx.fillRect(350, 280, 100, 60);
            ctx.fillStyle = 'white';
            ctx.fillText('NAS设备', 400, 305);
            ctx.fillText('(有自己的OS)', 400, 325);

            // 连接线
            ctx.strokeStyle = '#2ecc71';
            ctx.lineWidth = 4;
            ctx.beginPath();
            ctx.moveTo(400, 220);
            ctx.lineTo(400, 280);
            ctx.stroke();

            // 多台计算机
            const computers = [
                {x: 150, y: 100, label: 'Windows'},
                {x: 400, y: 100, label: 'Mac'},
                {x: 650, y: 100, label: 'Linux'}
            ];

            computers.forEach(comp => {
                ctx.fillStyle = '#34495e';
                ctx.fillRect(comp.x - 40, comp.y - 20, 80, 40);
                ctx.fillStyle = 'white';
                ctx.fillText(comp.label, comp.x, comp.y + 5);

                // 连接到交换机
                ctx.strokeStyle = '#2ecc71';
                ctx.lineWidth = 3;
                ctx.beginPath();
                ctx.moveTo(comp.x, comp.y + 20);
                ctx.lineTo(400, 180);
                ctx.stroke();
            });

            // 说明文字
            ctx.fillStyle = '#34495e';
            ctx.font = '12px Microsoft YaHei';
            ctx.fillText('支持跨平台文件共享', 400, 370);
        }

        // 绘制SAN架构
        function drawSANArchitecture(ctx) {
            ctx.clearRect(0, 0, 800, 400);

            // FC SAN部分
            ctx.fillStyle = '#e74c3c';
            ctx.fillRect(100, 100, 250, 120);
            ctx.fillStyle = 'white';
            ctx.font = '16px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('FC SAN', 225, 130);
            ctx.font = '12px Microsoft YaHei';
            ctx.fillText('光纤通道网络', 225, 150);
            ctx.fillText('高速专用连接', 225, 170);
            ctx.fillText('距离可达10km', 225, 190);

            // IP SAN部分
            ctx.fillStyle = '#3498db';
            ctx.fillRect(450, 100, 250, 120);
            ctx.fillStyle = 'white';
            ctx.font = '16px Microsoft YaHei';
            ctx.fillText('IP SAN', 575, 130);
            ctx.font = '12px Microsoft YaHei';
            ctx.fillText('标准以太网', 575, 150);
            ctx.fillText('使用TCP/IP协议', 575, 170);
            ctx.fillText('成本较低', 575, 190);

            // 存储设备
            ctx.fillStyle = '#2ecc71';
            ctx.fillRect(300, 280, 200, 80);
            ctx.fillStyle = 'white';
            ctx.font = '14px Microsoft YaHei';
            ctx.fillText('共享存储设备', 400, 315);
            ctx.fillText('块级存储访问', 400, 335);

            // 连接线
            ctx.strokeStyle = '#e74c3c';
            ctx.lineWidth = 4;
            ctx.beginPath();
            ctx.moveTo(225, 220);
            ctx.lineTo(350, 280);
            ctx.stroke();

            ctx.strokeStyle = '#3498db';
            ctx.beginPath();
            ctx.moveTo(575, 220);
            ctx.lineTo(450, 280);
            ctx.stroke();
        }

        // 绘制对比图表
        function drawComparisonChart(ctx) {
            ctx.clearRect(0, 0, 800, 500);

            const features = [
                {name: '连接方式', das: 'SCSI直连', nas: '网络连接', san: '专用网络'},
                {name: '跨平台', das: '不支持', nas: '支持', san: '支持'},
                {name: '独立性', das: '依赖服务器', nas: '独立工作', san: '独立工作'},
                {name: '文件系统', das: '服务器管理', nas: '自带OS', san: '块级访问'},
                {name: '成本', das: '低', nas: '中等', san: '高'}
            ];

            // 绘制表格
            ctx.strokeStyle = '#34495e';
            ctx.lineWidth = 2;
            ctx.font = '14px Microsoft YaHei';
            ctx.textAlign = 'center';

            // 表头
            const colWidth = 180;
            const rowHeight = 60;
            const startX = 50;
            const startY = 50;

            ['特征', 'DAS', 'NAS', 'SAN'].forEach((header, i) => {
                ctx.fillStyle = '#667eea';
                ctx.fillRect(startX + i * colWidth, startY, colWidth, rowHeight);
                ctx.fillStyle = 'white';
                ctx.fillText(header, startX + i * colWidth + colWidth/2, startY + 35);
            });

            // 数据行
            features.forEach((feature, rowIndex) => {
                const y = startY + (rowIndex + 1) * rowHeight;

                // 特征名称
                ctx.fillStyle = '#f8f9fa';
                ctx.fillRect(startX, y, colWidth, rowHeight);
                ctx.fillStyle = '#34495e';
                ctx.fillText(feature.name, startX + colWidth/2, y + 35);

                // DAS
                ctx.fillStyle = '#ffebee';
                ctx.fillRect(startX + colWidth, y, colWidth, rowHeight);
                ctx.fillStyle = '#c62828';
                ctx.fillText(feature.das, startX + colWidth + colWidth/2, y + 35);

                // NAS
                ctx.fillStyle = '#e8f5e8';
                ctx.fillRect(startX + 2 * colWidth, y, colWidth, rowHeight);
                ctx.fillStyle = '#2e7d32';
                ctx.fillText(feature.nas, startX + 2 * colWidth + colWidth/2, y + 35);

                // SAN
                ctx.fillStyle = '#e3f2fd';
                ctx.fillRect(startX + 3 * colWidth, y, colWidth, rowHeight);
                ctx.fillStyle = '#1565c0';
                ctx.fillText(feature.san, startX + 3 * colWidth + colWidth/2, y + 35);
            });

            // 绘制边框
            for (let i = 0; i <= 4; i++) {
                ctx.beginPath();
                ctx.moveTo(startX + i * colWidth, startY);
                ctx.lineTo(startX + i * colWidth, startY + 6 * rowHeight);
                ctx.stroke();
            }

            for (let i = 0; i <= 6; i++) {
                ctx.beginPath();
                ctx.moveTo(startX, startY + i * rowHeight);
                ctx.lineTo(startX + 4 * colWidth, startY + i * rowHeight);
                ctx.stroke();
            }
        }

        // 开始介绍动画
        function startIntroAnimation() {
            const canvas = document.getElementById('introCanvas');
            const ctx = canvas.getContext('2d');

            let frame = 0;
            function animate() {
                drawNetworkOverview(ctx);

                // 添加数据流动画
                const time = frame * 0.1;
                ctx.fillStyle = `rgba(102, 126, 234, ${0.5 + 0.3 * Math.sin(time)})`;
                ctx.beginPath();
                ctx.arc(400 + 50 * Math.cos(time), 200 + 30 * Math.sin(time), 5, 0, Math.PI * 2);
                ctx.fill();

                frame++;
                if (frame < 100) {
                    requestAnimationFrame(animate);
                }
            }
            animate();

            gsap.timeline()
                .to('.storage-card', {duration: 0.5, scale: 1.05, stagger: 0.1})
                .to('.storage-card', {duration: 0.5, scale: 1, stagger: 0.1});
        }

        // NAS连接动画
        function animateNASConnection() {
            const canvas = document.getElementById('nasCanvas');
            const ctx = canvas.getContext('2d');

            let frame = 0;
            function animate() {
                drawNASArchitecture(ctx);

                // 数据包动画
                const progress = (frame % 60) / 60;
                const computers = [{x: 150, y: 120}, {x: 400, y: 120}, {x: 650, y: 120}];

                computers.forEach((comp, i) => {
                    const delay = i * 20;
                    if (frame > delay) {
                        const localProgress = ((frame - delay) % 60) / 60;
                        const x = comp.x + (400 - comp.x) * localProgress;
                        const y = comp.y + (200 - comp.y) * localProgress;

                        ctx.fillStyle = '#ff6b6b';
                        ctx.beginPath();
                        ctx.arc(x, y, 8, 0, Math.PI * 2);
                        ctx.fill();

                        ctx.fillStyle = 'white';
                        ctx.font = '10px Microsoft YaHei';
                        ctx.textAlign = 'center';
                        ctx.fillText('数据', x, y + 3);
                    }
                });

                frame++;
                if (frame < 180) {
                    requestAnimationFrame(animate);
                }
            }
            animate();
        }

        // 显示SAN类型
        function showSANType(type) {
            const cards = document.querySelectorAll('#section-5 .storage-card');
            cards.forEach(card => card.classList.remove('selected'));

            event.target.closest('.storage-card').classList.add('selected');

            const explanation = document.getElementById('san-explanation');

            if (type === 'fc') {
                explanation.innerHTML = `
                    <h4>🔥 FC SAN (光纤通道SAN)</h4>
                    <ul>
                        <li>✅ 使用光纤通道作为网络介质</li>
                        <li>✅ 传输速度极快（2-128 Gbps）</li>
                        <li>✅ 传输距离远（最远10公里）</li>
                        <li>❌ 成本高，需要专用设备</li>
                    </ul>
                `;
            } else {
                explanation.innerHTML = `
                    <h4>🌐 IP SAN (以太网SAN)</h4>
                    <ul>
                        <li>✅ 使用标准以太网作为网络介质</li>
                        <li>✅ 成本相对较低</li>
                        <li>✅ 可以利用现有网络基础设施</li>
                        <li>❌ 速度相对较慢</li>
                    </ul>
                `;
            }

            gsap.from(explanation, {duration: 0.5, opacity: 0, y: 20});
        }

        // 解释错误选项
        function explainWrongOption(option) {
            const explanation = document.getElementById('wrong-explanation');
            let content = '';

            switch(option) {
                case 'A':
                    content = `
                        <h4>❌ 为什么选项A错误？</h4>
                        <p><strong>DAS（直接附加存储）的真实情况：</strong></p>
                        <ul>
                            <li>DAS通过SCSI电缆直接连接到单台服务器</li>
                            <li>所有文件访问都必须通过这台服务器</li>
                            <li>不同操作系统的文件格式不兼容</li>
                            <li>无法实现真正的跨平台文件共享</li>
                        </ul>
                    `;
                    break;
                case 'B':
                    content = `
                        <h4>❌ 为什么选项B错误？</h4>
                        <p><strong>NAS（网络附加存储）的真实情况：</strong></p>
                        <ul>
                            <li>NAS直接连接到网络，不是连接到服务器</li>
                            <li>NAS有自己的网卡和操作系统</li>
                            <li>客户端直接通过网络访问NAS</li>
                            <li>不需要通过服务器中转数据</li>
                        </ul>
                    `;
                    break;
                case 'D':
                    content = `
                        <h4>❌ 为什么选项D错误？</h4>
                        <p><strong>实际情况正好相反：</strong></p>
                        <ul>
                            <li>NAS有自己的操作系统和文件管理系统</li>
                            <li>SAN提供的是块级存储，没有文件系统</li>
                            <li>SAN上的文件系统由连接的服务器管理</li>
                            <li>这个选项完全颠倒了事实</li>
                        </ul>
                    `;
                    break;
            }

            explanation.innerHTML = content;
            gsap.from(explanation, {duration: 0.5, opacity: 0, y: 20});
        }

        // DAS连接演示
        function showDASConnection(component) {
            const cards = document.querySelectorAll('.storage-card');
            cards.forEach(card => card.classList.remove('selected'));

            event.target.closest('.storage-card').classList.add('selected');

            // 根据选择的组件显示不同信息
            let message = '';
            switch(component) {
                case 'server':
                    message = '服务器直接管理存储设备，所有数据请求都通过服务器处理';
                    break;
                case 'cable':
                    message = 'SCSI电缆提供高速直接连接，但距离有限，通常不超过25米';
                    break;
                case 'storage':
                    message = '存储设备没有自己的操作系统，完全依赖服务器管理';
                    break;
            }

            // 显示解释
            const explanation = document.querySelector('#section-3 .explanation');
            explanation.innerHTML = `<strong>💡 ${message}</strong>`;

            // 动画效果
            gsap.from(explanation, {duration: 0.5, opacity: 0, y: 20});
        }

        // 初始化拖拽游戏
        function initDragGame() {
            const dragItems = document.querySelectorAll('.drag-item');
            const dropAreas = document.querySelectorAll('.drop-area');

            let draggedItem = null;

            // 为每个可拖拽项添加事件监听
            dragItems.forEach(item => {
                item.addEventListener('dragstart', function() {
                    draggedItem = this;
                    setTimeout(() => this.style.opacity = '0.5', 0);
                });

                item.addEventListener('dragend', function() {
                    this.style.opacity = '1';
                });
            });

            // 为每个放置区域添加事件监听
            dropAreas.forEach(area => {
                area.addEventListener('dragover', function(e) {
                    e.preventDefault();
                    this.style.background = 'rgba(102, 126, 234, 0.2)';
                });

                area.addEventListener('dragleave', function() {
                    this.style.background = 'transparent';
                });

                area.addEventListener('drop', function(e) {
                    e.preventDefault();
                    this.style.background = 'transparent';

                    if (draggedItem) {
                        const correctAnswer = draggedItem.getAttribute('data-answer');
                        const dropZoneType = this.parentElement.getAttribute('data-type');

                        if (correctAnswer === dropZoneType) {
                            // 正确答案
                            this.appendChild(draggedItem);
                            draggedItem.style.background = '#d4edda';
                            draggedItem.style.borderColor = '#28a745';

                            // 动画效果
                            gsap.from(draggedItem, {duration: 0.5, scale: 0.8, ease: 'back.out'});
                        } else {
                            // 错误答案，添加震动效果
                            gsap.to(draggedItem, {
                                duration: 0.1,
                                x: -5,
                                yoyo: true,
                                repeat: 5,
                                onComplete: () => {
                                    draggedItem.style.transform = 'translateX(0)';
                                }
                            });
                        }
                    }
                });
            });
        }

        // 最终测试
        function setupFinalTest() {
            const options = document.querySelectorAll('[data-final]');
            const resultDiv = document.getElementById('final-result');

            options.forEach(option => {
                option.addEventListener('click', function() {
                    const selected = this.getAttribute('data-final');

                    options.forEach(opt => {
                        opt.classList.remove('correct', 'wrong');
                    });

                    if (selected === 'C') {
                        this.classList.add('correct');
                        resultDiv.classList.remove('hidden');

                        // 庆祝动画
                        gsap.from(resultDiv, {duration: 0.8, opacity: 0, y: 30, ease: 'back.out'});

                        // 烟花效果
                        createFireworks();
                    } else {
                        this.classList.add('wrong');
                        document.querySelector('[data-final="C"]').classList.add('correct');
                    }
                });
            });
        }

        // 创建烟花效果
        function createFireworks() {
            const container = document.querySelector('#section-8');

            for (let i = 0; i < 20; i++) {
                const firework = document.createElement('div');
                firework.className = 'firework';
                firework.style.position = 'absolute';
                firework.style.width = '10px';
                firework.style.height = '10px';
                firework.style.borderRadius = '50%';
                firework.style.background = `hsl(${Math.random() * 360}, 100%, 50%)`;
                firework.style.left = `${Math.random() * 100}%`;
                firework.style.top = `${Math.random() * 100}%`;

                container.appendChild(firework);

                gsap.to(firework, {
                    duration: 1 + Math.random(),
                    scale: 0,
                    opacity: 0,
                    ease: 'power2.out',
                    onComplete: () => {
                        firework.remove();
                    }
                });
            }
        }

        // 重新开始学习
        function restartLearning() {
            currentSection = 1;
            document.querySelectorAll('.section').forEach((section, index) => {
                if (index === 0) {
                    section.classList.add('active');
                } else {
                    section.classList.remove('active');
                }
            });
            updateProgress();
            window.scrollTo(0, 0);
        }

        // 导航功能
        function nextSection() {
            if (currentSection < totalSections) {
                document.getElementById(`section-${currentSection}`).classList.remove('active');
                currentSection++;
                
                // 如果下一个section存在，显示它
                const nextSectionElement = document.getElementById(`section-${currentSection}`);
                if (nextSectionElement) {
                    gsap.to(nextSectionElement, {
                        duration: 0.5,
                        opacity: 1,
                        y: 0,
                        onStart: () => {
                            nextSectionElement.classList.add('active');
                            nextSectionElement.style.display = 'block';
                        }
                    });
                }
                
                updateProgress();
                initCanvas();
            }
        }

        function prevSection() {
            if (currentSection > 1) {
                document.getElementById(`section-${currentSection}`).classList.remove('active');
                currentSection--;
                document.getElementById(`section-${currentSection}`).classList.add('active');
                updateProgress();
                initCanvas();
            }
        }

        function updateProgress() {
            const progress = (currentSection / totalSections) * 100;
            gsap.to('#progress', {duration: 0.5, width: `${progress}%`});
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            initCanvas();
            updateProgress();

            // 初始化拖拽游戏
            setTimeout(() => {
                initDragGame();
            }, 1000);

            // 设置最终测试
            setTimeout(() => {
                setupFinalTest();
            }, 1000);

            // 为选项添加点击事件
            document.querySelectorAll('.option').forEach(option => {
                option.addEventListener('click', function() {
                    const selectedOption = this.dataset.option;
                    document.querySelectorAll('.option').forEach(opt => {
                        opt.classList.remove('correct', 'wrong');
                    });

                    if (selectedOption === 'C') {
                        this.classList.add('correct');
                    } else {
                        this.classList.add('wrong');
                        document.querySelector('[data-option="C"]').classList.add('correct');
                    }
                });
            });

            // 添加键盘导航
            document.addEventListener('keydown', function(e) {
                if (e.key === 'ArrowRight' || e.key === ' ') {
                    nextSection();
                } else if (e.key === 'ArrowLeft') {
                    prevSection();
                }
            });

            // 添加滚动监听，自动显示section
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.classList.add('active');
                        gsap.from(entry.target, {duration: 0.8, opacity: 0, y: 50});
                    }
                });
            }, {threshold: 0.1});

            document.querySelectorAll('.section').forEach(section => {
                observer.observe(section);
            });

            // 添加页面入场动画
            gsap.from('h1', {duration: 1, opacity: 0, y: -50, ease: 'back.out'});
            gsap.from('.question-box', {duration: 1, opacity: 0, scale: 0.9, delay: 0.3});
            gsap.from('.btn', {duration: 0.5, opacity: 0, y: 20, stagger: 0.1, delay: 0.6});
        });
    </script>
</body>
</html>
