<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>URL结构学习 - 互动教学</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 50px;
            animation: fadeInDown 1s ease-out;
        }

        .title {
            color: white;
            font-size: 2.5rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .subtitle {
            color: rgba(255,255,255,0.8);
            font-size: 1.2rem;
        }

        .learning-section {
            background: white;
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            animation: fadeInUp 1s ease-out;
        }

        .url-display {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 15px;
            padding: 20px;
            font-family: 'Courier New', monospace;
            font-size: 1.5rem;
            text-align: center;
            margin: 30px 0;
            position: relative;
            overflow: hidden;
        }

        .url-part {
            display: inline-block;
            padding: 8px 12px;
            margin: 2px;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
        }

        .protocol { background: #ff6b6b; color: white; }
        .domain { background: #4ecdc4; color: white; }
        .path { background: #45b7d1; color: white; }
        .file { background: #96ceb4; color: white; }

        .url-part:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .explanation-box {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 25px;
            border-radius: 15px;
            margin: 20px 0;
            opacity: 0;
            transform: translateY(20px);
            transition: all 0.5s ease;
        }

        .explanation-box.show {
            opacity: 1;
            transform: translateY(0);
        }

        .canvas-container {
            text-align: center;
            margin: 30px 0;
        }

        #animationCanvas {
            border: 2px solid #ddd;
            border-radius: 15px;
            background: white;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .quiz-section {
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            border-radius: 20px;
            padding: 40px;
            margin-top: 30px;
        }

        .quiz-question {
            font-size: 1.3rem;
            margin-bottom: 20px;
            color: #333;
        }

        .quiz-options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }

        .quiz-option {
            background: white;
            border: 2px solid #ddd;
            border-radius: 10px;
            padding: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
        }

        .quiz-option:hover {
            border-color: #667eea;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .quiz-option.correct {
            background: #d4edda;
            border-color: #28a745;
            animation: pulse 0.5s ease-in-out;
        }

        .quiz-option.wrong {
            background: #f8d7da;
            border-color: #dc3545;
            animation: shake 0.5s ease-in-out;
        }

        .progress-bar {
            width: 100%;
            height: 10px;
            background: #e9ecef;
            border-radius: 5px;
            overflow: hidden;
            margin: 20px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            width: 0%;
            transition: width 1s ease;
        }

        @keyframes fadeInDown {
            from { opacity: 0; transform: translateY(-30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes fadeInUp {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }

        .floating-elements {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
        }

        .floating-element {
            position: absolute;
            width: 20px;
            height: 20px;
            background: rgba(255,255,255,0.1);
            border-radius: 50%;
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }
    </style>
</head>
<body>
    <div class="floating-elements" id="floatingElements"></div>
    
    <div class="container">
        <div class="header">
            <h1 class="title">🌐 URL结构探索之旅</h1>
            <p class="subtitle">让我们一起解密网址的秘密！</p>
        </div>

        <div class="learning-section">
            <h2>📚 什么是URL？</h2>
            <p style="font-size: 1.1rem; line-height: 1.6; margin: 20px 0;">
                URL就像是互联网上的地址，告诉浏览器去哪里找到你想要的网页。
                就像你家的地址一样，每个部分都有特殊的含义！
            </p>

            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>

            <div class="url-display" id="urlDisplay">
                <span class="url-part protocol" data-part="protocol">http://</span><span class="url-part domain" data-part="domain">www.test.com</span><span class="url-part path" data-part="path">/main/</span><span class="url-part file" data-part="file">index.htm</span>
            </div>

            <div class="explanation-box" id="explanationBox">
                点击上面URL的任意部分，我会为你详细解释！
            </div>

            <div class="canvas-container">
                <canvas id="animationCanvas" width="800" height="400"></canvas>
            </div>
        </div>

        <div class="quiz-section">
            <h2>🎯 现在来测试一下你的理解！</h2>
            <div class="quiz-question">
                在 http://www.test.com/main/index.htm 中，index.htm 是什么？
            </div>
            <div class="quiz-options">
                <div class="quiz-option" data-answer="A">A. 协议名</div>
                <div class="quiz-option" data-answer="B">B. 域名</div>
                <div class="quiz-option" data-answer="C">C. 主机名</div>
                <div class="quiz-option" data-answer="D">D. 页面文件</div>
            </div>
            <div id="quizResult" style="margin-top: 20px; font-size: 1.2rem; text-align: center;"></div>
        </div>
    </div>

    <script>
        // 创建浮动元素
        function createFloatingElements() {
            const container = document.getElementById('floatingElements');
            for (let i = 0; i < 20; i++) {
                const element = document.createElement('div');
                element.className = 'floating-element';
                element.style.left = Math.random() * 100 + '%';
                element.style.top = Math.random() * 100 + '%';
                element.style.animationDelay = Math.random() * 6 + 's';
                container.appendChild(element);
            }
        }

        // Canvas动画
        const canvas = document.getElementById('animationCanvas');
        const ctx = canvas.getContext('2d');
        let animationState = 'idle';

        function drawURLStructure() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 绘制URL结构图
            const parts = [
                { text: 'http://', x: 50, y: 200, color: '#ff6b6b', label: '协议' },
                { text: 'www.test.com', x: 200, y: 200, color: '#4ecdc4', label: '域名' },
                { text: '/main/', x: 400, y: 200, color: '#45b7d1', label: '路径' },
                { text: 'index.htm', x: 550, y: 200, color: '#96ceb4', label: '文件' }
            ];

            parts.forEach((part, index) => {
                // 绘制背景框
                ctx.fillStyle = part.color;
                ctx.fillRect(part.x - 10, part.y - 20, part.text.length * 12 + 20, 40);
                
                // 绘制文字
                ctx.fillStyle = 'white';
                ctx.font = '16px Arial';
                ctx.fillText(part.text, part.x, part.y + 5);
                
                // 绘制标签
                ctx.fillStyle = '#333';
                ctx.font = '14px Arial';
                ctx.fillText(part.label, part.x, part.y - 30);
                
                // 绘制箭头
                if (index < parts.length - 1) {
                    ctx.strokeStyle = '#666';
                    ctx.lineWidth = 2;
                    ctx.beginPath();
                    ctx.moveTo(part.x + part.text.length * 12 + 10, part.y);
                    ctx.lineTo(parts[index + 1].x - 10, parts[index + 1].y);
                    ctx.stroke();
                }
            });
        }

        // URL部分点击事件
        document.querySelectorAll('.url-part').forEach(part => {
            part.addEventListener('click', function() {
                const partType = this.dataset.part;
                const explanationBox = document.getElementById('explanationBox');
                
                let explanation = '';
                switch(partType) {
                    case 'protocol':
                        explanation = '🔗 <strong>协议名 (http://)</strong><br>这告诉浏览器用什么方式来获取网页。http是最常见的网页协议，就像是说"请用网页的方式来访问"。';
                        break;
                    case 'domain':
                        explanation = '🏠 <strong>域名 (www.test.com)</strong><br>这是网站的地址，就像你家的门牌号。浏览器通过这个地址找到存放网页的服务器。';
                        break;
                    case 'path':
                        explanation = '📁 <strong>路径 (/main/)</strong><br>这告诉服务器网页文件存放在哪个文件夹里，就像是房子里的房间号。';
                        break;
                    case 'file':
                        explanation = '📄 <strong>页面文件 (index.htm)</strong><br>这就是我们要访问的具体网页文件！就像是你要找的那本书的名字。';
                        break;
                }
                
                explanationBox.innerHTML = explanation;
                explanationBox.classList.add('show');
                
                // 更新进度条
                const progress = document.getElementById('progressFill');
                const currentWidth = parseInt(progress.style.width) || 0;
                progress.style.width = Math.min(currentWidth + 25, 100) + '%';
            });
        });

        // 测验功能
        document.querySelectorAll('.quiz-option').forEach(option => {
            option.addEventListener('click', function() {
                const answer = this.dataset.answer;
                const result = document.getElementById('quizResult');
                
                // 清除之前的样式
                document.querySelectorAll('.quiz-option').forEach(opt => {
                    opt.classList.remove('correct', 'wrong');
                });
                
                if (answer === 'D') {
                    this.classList.add('correct');
                    result.innerHTML = '🎉 <strong>正确！</strong><br>index.htm 确实是页面文件！它是我们要访问的具体网页文件名。';
                    result.style.color = '#28a745';
                } else {
                    this.classList.add('wrong');
                    document.querySelector('[data-answer="D"]').classList.add('correct');
                    result.innerHTML = '❌ <strong>不对哦！</strong><br>正确答案是 D. 页面文件。index.htm 是具体的网页文件名，不是协议、域名或主机名。';
                    result.style.color = '#dc3545';
                }
            });
        });

        // 初始化
        createFloatingElements();
        drawURLStructure();
        
        // 定期重绘canvas动画
        setInterval(drawURLStructure, 100);
    </script>
</body>
</html>
