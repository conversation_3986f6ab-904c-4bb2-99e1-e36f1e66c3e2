<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>域名服务器学习 - 交互式教学</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            color: white;
            margin-bottom: 40px;
            animation: fadeInDown 1s ease-out;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .learning-section {
            background: white;
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            animation: fadeInUp 1s ease-out;
        }

        .section-title {
            font-size: 1.8em;
            color: #333;
            margin-bottom: 30px;
            text-align: center;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        .dns-hierarchy {
            position: relative;
            height: 500px;
            margin: 40px 0;
        }

        .dns-server {
            position: absolute;
            background: white;
            border: 3px solid #667eea;
            border-radius: 15px;
            padding: 15px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .dns-server:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.2);
        }

        .dns-server.active {
            background: #667eea;
            color: white;
            transform: scale(1.1);
        }

        .root-server {
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: #ff6b6b;
            border-color: #ff6b6b;
            color: white;
        }

        .top-level-server {
            top: 150px;
            background: #4ecdc4;
            border-color: #4ecdc4;
            color: white;
        }

        .authoritative-server {
            top: 280px;
            background: #45b7d1;
            border-color: #45b7d1;
            color: white;
        }

        .local-server {
            top: 410px;
            left: 50%;
            transform: translateX(-50%);
            background: #96ceb4;
            border-color: #96ceb4;
            color: white;
        }

        .connection-line {
            position: absolute;
            background: #ddd;
            transition: all 0.3s ease;
        }

        .connection-line.active {
            background: #667eea;
            box-shadow: 0 0 10px rgba(102, 126, 234, 0.5);
        }

        .info-panel {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            margin: 20px 0;
            border-left: 5px solid #667eea;
            opacity: 0;
            transform: translateX(-20px);
            transition: all 0.5s ease;
        }

        .info-panel.show {
            opacity: 1;
            transform: translateX(0);
        }

        .quiz-section {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border-radius: 20px;
            padding: 40px;
            margin: 30px 0;
        }

        .option {
            background: rgba(255,255,255,0.1);
            border: 2px solid rgba(255,255,255,0.3);
            border-radius: 10px;
            padding: 15px;
            margin: 10px 0;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .option:hover {
            background: rgba(255,255,255,0.2);
            transform: translateX(10px);
        }

        .option.correct {
            background: #4CAF50;
            border-color: #4CAF50;
        }

        .option.wrong {
            background: #f44336;
            border-color: #f44336;
        }

        .btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1em;
            transition: all 0.3s ease;
            margin: 10px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .pulse {
            animation: pulse 2s infinite;
        }

        .canvas-container {
            text-align: center;
            margin: 30px 0;
        }

        #animationCanvas {
            border: 2px solid #ddd;
            border-radius: 10px;
            background: white;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🌐 域名服务器学习之旅</h1>
            <p>让我们一起探索DNS的奥秘，从零开始理解域名解析！</p>
        </div>

        <div class="learning-section">
            <h2 class="section-title">📚 什么是域名服务器？</h2>
            <div class="info-panel show">
                <h3>🤔 想象一下...</h3>
                <p>域名服务器就像是互联网的"电话簿"！当你想访问一个网站时，比如输入 www.baidu.com，计算机需要知道这个网站的真实地址（IP地址）才能找到它。域名服务器就是帮助我们把网站名字翻译成IP地址的"翻译官"。</p>
            </div>
        </div>

        <div class="learning-section">
            <h2 class="section-title">🏗️ DNS层次结构</h2>
            <p style="text-align: center; margin-bottom: 20px; color: #666;">点击下面的服务器，了解它们的作用！</p>
            
            <div class="dns-hierarchy">
                <div class="dns-server root-server" data-type="root">
                    <strong>根域名服务器</strong><br>
                    <small>最高层次</small>
                </div>
                
                <div class="dns-server top-level-server" style="left: 20%;" data-type="top-level">
                    <strong>.com域名服务器</strong><br>
                    <small>顶级域名</small>
                </div>
                
                <div class="dns-server top-level-server" style="left: 70%;" data-type="top-level">
                    <strong>.cn域名服务器</strong><br>
                    <small>顶级域名</small>
                </div>
                
                <div class="dns-server authoritative-server" style="left: 10%;" data-type="authoritative">
                    <strong>baidu.com权限服务器</strong><br>
                    <small>权限域名</small>
                </div>
                
                <div class="dns-server authoritative-server" style="left: 60%;" data-type="authoritative">
                    <strong>google.com权限服务器</strong><br>
                    <small>权限域名</small>
                </div>
                
                <div class="dns-server local-server" data-type="local">
                    <strong>本地域名服务器</strong><br>
                    <small>离你最近</small>
                </div>
            </div>
            
            <div id="server-info" class="info-panel">
                <h3 id="info-title">选择一个服务器了解详情</h3>
                <p id="info-content">点击上面的任意服务器，我会详细解释它的作用！</p>
            </div>
        </div>

        <div class="canvas-container">
            <h3>🎬 DNS查询动画演示</h3>
            <canvas id="animationCanvas" width="800" height="400"></canvas>
            <br>
            <button class="btn" onclick="startAnimation()">开始演示DNS查询过程</button>
        </div>

        <div class="learning-section">
            <h2 class="section-title">🧠 做题思路分析</h2>
            <div class="info-panel show">
                <h3>💡 解题三步法</h3>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin-top: 20px;">
                    <div style="background: #e3f2fd; padding: 20px; border-radius: 10px; border-left: 4px solid #2196f3;">
                        <h4>第一步：理解概念</h4>
                        <p>先搞清楚每种DNS服务器的作用和层次关系</p>
                    </div>
                    <div style="background: #f3e5f5; padding: 20px; border-radius: 10px; border-left: 4px solid #9c27b0;">
                        <h4>第二步：分析选项</h4>
                        <p>逐个分析每个选项的正确性</p>
                    </div>
                    <div style="background: #e8f5e8; padding: 20px; border-radius: 10px; border-left: 4px solid #4caf50;">
                        <h4>第三步：排除法</h4>
                        <p>找出明显错误的选项</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="quiz-section">
            <h2 style="text-align: center; margin-bottom: 30px;">📝 原题重现</h2>
            <div style="background: rgba(255,255,255,0.1); padding: 25px; border-radius: 15px; margin-bottom: 30px;">
                <h3 style="margin-bottom: 20px;">以下关于域名服务器的叙述，错误的是（ ）。</h3>

                <div class="option" data-answer="A" onclick="selectOption(this)">
                    <strong>A.</strong> 本地缓存域名服务不需要域名数据库
                </div>

                <div class="option" data-answer="B" onclick="selectOption(this)">
                    <strong>B.</strong> 顶级域名服务器是最高层次的域名服务器
                </div>

                <div class="option" data-answer="C" onclick="selectOption(this)">
                    <strong>C.</strong> 本地域名服务器可以采用递归查询和迭代查询两种查询方式
                </div>

                <div class="option" data-answer="D" onclick="selectOption(this)">
                    <strong>D.</strong> 权限服务器负责将其管辖区内的主机域名转换为该主机的IP地址
                </div>
            </div>

            <div style="text-align: center;">
                <button class="btn" onclick="showAnswer()">查看答案解析</button>
                <button class="btn" onclick="resetQuiz()">重新答题</button>
            </div>

            <div id="answer-explanation" style="display: none; margin-top: 30px; background: rgba(255,255,255,0.1); padding: 25px; border-radius: 15px;">
                <h3>📖 详细解析</h3>
                <div id="explanation-content"></div>
            </div>
        </div>

        <div class="learning-section">
            <h2 class="section-title">🎯 记忆口诀</h2>
            <div class="info-panel show" style="text-align: center; background: linear-gradient(135deg, #ffecd2, #fcb69f); border-left: 5px solid #ff8a65;">
                <h3 style="color: #d84315;">DNS层次记忆法</h3>
                <div style="font-size: 1.5em; margin: 20px 0; color: #bf360c; font-weight: bold;">
                    根 → 顶级 → 权限 → 本地<br>
                    <small style="font-size: 0.7em; color: #666;">（从高到低的管理层次）</small>
                </div>
                <p style="color: #5d4037; font-size: 1.1em;">
                    记住：<strong>根域名服务器</strong>是最高层次的！<br>
                    就像公司的CEO（根）→ 部门总监（顶级）→ 项目经理（权限）→ 你的助手（本地）
                </p>
            </div>
        </div>
    </div>

    <script>
        // DNS服务器信息
        const serverInfo = {
            root: {
                title: "🌍 根域名服务器 - 互联网的总指挥",
                content: "根域名服务器是DNS系统的最高层次！它就像互联网的总指挥部，知道所有顶级域名服务器的地址。全世界只有13个根域名服务器，它们是互联网的基础设施。当本地服务器不知道怎么解析域名时，就会来问根服务器：我该去哪里找.com的服务器？"
            },
            'top-level': {
                title: "🏢 顶级域名服务器 - 各个部门主管",
                content: "顶级域名服务器管理着各种顶级域名，比如.com、.cn、.org等。它们就像各个部门的主管，负责管理自己部门下的所有二级域名。比如.com服务器知道baidu.com、google.com等所有.com域名的权限服务器在哪里。注意：顶级域名服务器不是最高层次的，根域名服务器才是！"
            },
            authoritative: {
                title: "📋 权限域名服务器 - 具体的档案管理员",
                content: "权限域名服务器是真正存储域名和IP地址对应关系的地方！它就像档案管理员，负责管理某个具体域名下所有主机的信息。比如baidu.com的权限服务器知道www.baidu.com、map.baidu.com等所有百度子域名的IP地址。"
            },
            local: {
                title: "🏠 本地域名服务器 - 你的贴身助手",
                content: "本地域名服务器是离你最近的DNS服务器，通常由你的网络服务商提供。它就像你的贴身助手，会记住你经常访问的网站地址（缓存），这样下次访问就更快了。它可以使用递归查询（帮你跑腿到底）和迭代查询（告诉你下一步该找谁）两种方式工作。"
            }
        };

        // 点击服务器显示信息
        document.querySelectorAll('.dns-server').forEach(server => {
            server.addEventListener('click', function() {
                // 移除所有active状态
                document.querySelectorAll('.dns-server').forEach(s => s.classList.remove('active'));
                // 添加当前active状态
                this.classList.add('active');
                
                const type = this.dataset.type;
                const info = serverInfo[type];
                
                const infoPanel = document.getElementById('server-info');
                const infoTitle = document.getElementById('info-title');
                const infoContent = document.getElementById('info-content');
                
                infoTitle.textContent = info.title;
                infoContent.textContent = info.content;
                
                infoPanel.classList.add('show');
                
                // 添加脉冲动画
                this.classList.add('pulse');
                setTimeout(() => {
                    this.classList.remove('pulse');
                }, 2000);
            });
        });

        // Canvas动画
        let canvas, ctx;

        // 确保DOM加载完成后再获取canvas元素
        function initCanvas() {
            canvas = document.getElementById('animationCanvas');
            if (canvas) {
                ctx = canvas.getContext('2d');
            }
        }

        function startAnimation() {
            if (!canvas || !ctx) {
                initCanvas();
            }

            if (!canvas || !ctx) {
                console.error('Canvas not found');
                return;
            }

            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // 绘制DNS查询流程动画
            let step = 0;
            const steps = [
                { text: "1. 用户输入 www.baidu.com", x: 50, y: 350 },
                { text: "2. 查询本地DNS服务器", x: 200, y: 300 },
                { text: "3. 询问根域名服务器", x: 400, y: 100 },
                { text: "4. 根服务器指向.com服务器", x: 600, y: 150 },
                { text: "5. .com服务器指向baidu.com权限服务器", x: 500, y: 250 },
                { text: "6. 获得IP地址并返回给用户", x: 100, y: 200 }
            ];

            function animateStep() {
                if (step < steps.length) {
                    const currentStep = steps[step];

                    // 绘制步骤圆点
                    ctx.fillStyle = '#667eea';
                    ctx.beginPath();
                    ctx.arc(currentStep.x, currentStep.y, 8, 0, 2 * Math.PI);
                    ctx.fill();

                    // 绘制步骤文字
                    ctx.fillStyle = '#333';
                    ctx.font = '14px Microsoft YaHei';
                    ctx.fillText(currentStep.text, currentStep.x + 20, currentStep.y + 5);

                    // 绘制连接线
                    if (step > 0) {
                        const prevStep = steps[step - 1];
                        ctx.strokeStyle = '#667eea';
                        ctx.lineWidth = 3;
                        ctx.beginPath();
                        ctx.moveTo(prevStep.x, prevStep.y);
                        ctx.lineTo(currentStep.x, currentStep.y);
                        ctx.stroke();

                        // 绘制箭头
                        const angle = Math.atan2(currentStep.y - prevStep.y, currentStep.x - prevStep.x);
                        const arrowLength = 15;
                        ctx.beginPath();
                        ctx.moveTo(currentStep.x, currentStep.y);
                        ctx.lineTo(currentStep.x - arrowLength * Math.cos(angle - Math.PI / 6),
                                  currentStep.y - arrowLength * Math.sin(angle - Math.PI / 6));
                        ctx.moveTo(currentStep.x, currentStep.y);
                        ctx.lineTo(currentStep.x - arrowLength * Math.cos(angle + Math.PI / 6),
                                  currentStep.y - arrowLength * Math.sin(angle + Math.PI / 6));
                        ctx.stroke();
                    }

                    step++;
                    setTimeout(animateStep, 1500);
                }
            }

            animateStep();
        }

        // 题目交互功能
        let selectedAnswer = null;
        let answered = false;

        function selectOption(element) {
            if (answered) return;

            // 移除之前的选择
            document.querySelectorAll('.option').forEach(opt => {
                opt.style.background = 'rgba(255,255,255,0.1)';
                opt.style.transform = 'translateX(0)';
            });

            // 高亮当前选择
            element.style.background = 'rgba(255,255,255,0.3)';
            element.style.transform = 'translateX(10px)';
            selectedAnswer = element.dataset.answer;
        }

        function showAnswer() {
            answered = true;
            const options = document.querySelectorAll('.option');
            const explanationDiv = document.getElementById('answer-explanation');
            const explanationContent = document.getElementById('explanation-content');

            // 显示正确答案（B是错误的）
            options.forEach(option => {
                if (option.dataset.answer === 'B') {
                    option.classList.add('correct');
                    option.innerHTML += ' <span style="float: right;">✓ 正确答案</span>';
                } else {
                    option.style.opacity = '0.6';
                }
            });

            // 显示用户选择结果
            if (selectedAnswer) {
                const userOption = document.querySelector(`[data-answer="${selectedAnswer}"]`);
                if (selectedAnswer !== 'B') {
                    userOption.classList.add('wrong');
                    userOption.innerHTML += ' <span style="float: right;">✗ 你的选择</span>';
                }
            }

            // 显示详细解析
            explanationContent.innerHTML = `
                <div style="margin-bottom: 20px;">
                    <h4 style="color: #4CAF50;">✅ 正确答案：B</h4>
                    <p><strong>顶级域名服务器是最高层次的域名服务器</strong> - 这个说法是错误的！</p>
                </div>

                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 15px;">
                    <div style="background: rgba(76, 175, 80, 0.1); padding: 15px; border-radius: 8px; border-left: 4px solid #4CAF50;">
                        <h5>✓ A选项 - 正确</h5>
                        <p>本地缓存确实不需要域名数据库，它只是临时存储最近查询的结果。</p>
                    </div>

                    <div style="background: rgba(244, 67, 54, 0.1); padding: 15px; border-radius: 8px; border-left: 4px solid #f44336;">
                        <h5>✗ B选项 - 错误</h5>
                        <p><strong>根域名服务器</strong>才是最高层次的！顶级域名服务器只是第二层。</p>
                    </div>

                    <div style="background: rgba(76, 175, 80, 0.1); padding: 15px; border-radius: 8px; border-left: 4px solid #4CAF50;">
                        <h5>✓ C选项 - 正确</h5>
                        <p>本地域名服务器确实可以使用递归查询和迭代查询两种方式。</p>
                    </div>

                    <div style="background: rgba(76, 175, 80, 0.1); padding: 15px; border-radius: 8px; border-left: 4px solid #4CAF50;">
                        <h5>✓ D选项 - 正确</h5>
                        <p>权限服务器确实负责将其管辖区内的主机域名转换为IP地址。</p>
                    </div>
                </div>

                <div style="margin-top: 25px; padding: 20px; background: rgba(255, 193, 7, 0.1); border-radius: 10px; border-left: 4px solid #FFC107;">
                    <h4 style="color: #F57C00;">🎯 关键记忆点</h4>
                    <p style="font-size: 1.1em; line-height: 1.6;">
                        DNS层次结构：<strong>根域名服务器</strong> > 顶级域名服务器 > 权限域名服务器 > 本地域名服务器<br>
                        <small>记住：根域名服务器是"总司令"，顶级域名服务器只是"部门经理"！</small>
                    </p>
                </div>
            `;

            explanationDiv.style.display = 'block';
            explanationDiv.scrollIntoView({ behavior: 'smooth' });
        }

        function resetQuiz() {
            answered = false;
            selectedAnswer = null;

            document.querySelectorAll('.option').forEach(option => {
                option.classList.remove('correct', 'wrong');
                option.style.background = 'rgba(255,255,255,0.1)';
                option.style.opacity = '1';
                option.style.transform = 'translateX(0)';
                option.innerHTML = option.innerHTML.split('<span')[0]; // 移除添加的标记
            });

            document.getElementById('answer-explanation').style.display = 'none';
        }

        // 页面加载动画
        window.addEventListener('load', function() {
            // 初始化canvas
            initCanvas();

            setTimeout(() => {
                const hierarchy = document.querySelector('.dns-hierarchy');
                if (hierarchy) {
                    hierarchy.style.opacity = '1';
                }
            }, 500);
        });
    </script>
</body>
</html>
