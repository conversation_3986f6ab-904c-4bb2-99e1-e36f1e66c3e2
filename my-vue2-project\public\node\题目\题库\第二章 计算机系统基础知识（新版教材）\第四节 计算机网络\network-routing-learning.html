<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>网络路由管理距离 - 互动学习</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            color: white;
        }

        .header h1 {
            font-size: 3rem;
            margin-bottom: 20px;
            opacity: 0;
            transform: translateY(-50px);
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
            opacity: 0;
            transform: translateY(30px);
        }

        .learning-section {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            opacity: 0;
            transform: translateY(50px);
        }

        .section-title {
            font-size: 2rem;
            color: #333;
            margin-bottom: 30px;
            text-align: center;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        .canvas-container {
            display: flex;
            justify-content: center;
            margin: 30px 0;
        }

        canvas {
            border: 2px solid #e0e0e0;
            border-radius: 15px;
            background: #f8f9fa;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .concept-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin: 40px 0;
        }

        .concept-card {
            background: white;
            border-radius: 15px;
            padding: 30px;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            cursor: pointer;
            opacity: 0;
            transform: translateY(30px);
        }

        .concept-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        }

        .concept-icon {
            width: 80px;
            height: 80px;
            margin: 0 auto 20px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            color: white;
            background: linear-gradient(135deg, #667eea, #764ba2);
        }

        .interactive-demo {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 30px;
            margin: 30px 0;
            text-align: center;
        }

        .demo-button {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1.1rem;
            cursor: pointer;
            margin: 10px;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .demo-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
        }

        .quiz-section {
            background: linear-gradient(135deg, #ff9a9e, #fecfef);
            color: white;
        }

        .quiz-option {
            background: rgba(255, 255, 255, 0.2);
            border: 2px solid transparent;
            border-radius: 10px;
            padding: 15px;
            margin: 10px 0;
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .quiz-option:hover {
            background: rgba(255, 255, 255, 0.3);
            border-color: white;
        }

        .quiz-option.correct {
            background: rgba(76, 175, 80, 0.8);
            border-color: #4CAF50;
        }

        .quiz-option.wrong {
            background: rgba(244, 67, 54, 0.8);
            border-color: #f44336;
        }

        .explanation {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
            backdrop-filter: blur(10px);
            display: none;
        }

        .progress-bar {
            width: 100%;
            height: 6px;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 3px;
            margin: 20px 0;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4CAF50, #8BC34A);
            width: 0%;
            border-radius: 3px;
            transition: width 1s ease;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }

        .floating {
            animation: float 3s ease-in-out infinite;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .pulse {
            animation: pulse 2s ease-in-out infinite;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🌐 网络路由管理距离</h1>
            <p>让我们一起探索网络世界的奥秘！</p>
        </div>

        <!-- 基础概念介绍 -->
        <div class="learning-section" id="concept-section">
            <h2 class="section-title">💡 什么是管理距离？</h2>
            <div class="canvas-container">
                <canvas id="conceptCanvas" width="800" height="400"></canvas>
            </div>
            <div class="interactive-demo">
                <button class="demo-button" onclick="startConceptAnimation()">🎬 开始动画演示</button>
                <button class="demo-button" onclick="resetAnimation()">🔄 重新开始</button>
            </div>
        </div>

        <!-- 管理距离表 -->
        <div class="learning-section" id="table-section">
            <h2 class="section-title">📊 管理距离对照表</h2>
            <div class="canvas-container">
                <canvas id="tableCanvas" width="800" height="500"></canvas>
            </div>
            <div class="interactive-demo">
                <button class="demo-button" onclick="showDistanceTable()">📋 显示距离表</button>
                <button class="demo-button" onclick="highlightDistance(15)">🔍 高亮距离15</button>
            </div>
        </div>

        <!-- 路由选择演示 -->
        <div class="learning-section" id="routing-section">
            <h2 class="section-title">🛣️ 路由选择过程</h2>
            <div class="canvas-container">
                <canvas id="routingCanvas" width="800" height="400"></canvas>
            </div>
            <div class="interactive-demo">
                <button class="demo-button" onclick="simulateRouting()">🚀 模拟路由选择</button>
                <button class="demo-button" onclick="compareProtocols()">⚖️ 协议对比</button>
            </div>
        </div>

        <!-- 题目解析 -->
        <div class="learning-section quiz-section" id="quiz-section">
            <h2 class="section-title">🎯 题目解析</h2>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
            
            <h3 style="margin-bottom: 20px;">如果管理距离为15，则（ ）。</h3>
            
            <div class="quiz-option" onclick="selectOption(this, false)">
                A. 这是一条静态路由
            </div>
            <div class="quiz-option" onclick="selectOption(this, false)">
                B. 这是一台直连设备
            </div>
            <div class="quiz-option" onclick="selectOption(this, true)">
                C. 该路由信息比较可靠
            </div>
            <div class="quiz-option" onclick="selectOption(this, false)">
                D. 该路由代价较小
            </div>

            <div class="explanation" id="explanation">
                <h4>🎓 详细解析：</h4>
                <p>管理距离15表示这是一个相对可靠的路由信息。在网络中，管理距离越小，路由信息越可靠。常见的管理距离值：</p>
                <ul style="text-align: left; margin: 15px 0;">
                    <li>直连接口：0（最可靠）</li>
                    <li>静态路由：1</li>
                    <li>EIGRP：90</li>
                    <li>OSPF：110</li>
                    <li>RIP：120</li>
                </ul>
                <p>距离15相对较小，说明该路由信息比较可靠！</p>
            </div>
        </div>
    </div>

    <script>
        // 初始化动画
        gsap.timeline()
            .to('.header h1', {duration: 1, opacity: 1, y: 0, ease: 'back.out(1.7)'})
            .to('.header p', {duration: 0.8, opacity: 1, y: 0}, '-=0.5')
            .to('.learning-section', {duration: 0.8, opacity: 1, y: 0, stagger: 0.2}, '-=0.3')
            .to('.concept-card', {duration: 0.6, opacity: 1, y: 0, stagger: 0.1}, '-=0.5');

        // Canvas 相关变量
        const conceptCanvas = document.getElementById('conceptCanvas');
        const conceptCtx = conceptCanvas.getContext('2d');
        const tableCanvas = document.getElementById('tableCanvas');
        const tableCtx = tableCanvas.getContext('2d');
        const routingCanvas = document.getElementById('routingCanvas');
        const routingCtx = routingCanvas.getContext('2d');

        let animationFrame;
        let animationStep = 0;

        // 管理距离数据
        const distanceData = [
            {protocol: '直连接口', distance: 0, color: '#4CAF50'},
            {protocol: '静态路由', distance: 1, color: '#2196F3'},
            {protocol: 'EIGRP', distance: 90, color: '#FF9800'},
            {protocol: 'OSPF', distance: 110, color: '#9C27B0'},
            {protocol: 'RIP', distance: 120, color: '#F44336'}
        ];

        // 绘制基础概念动画
        function startConceptAnimation() {
            animationStep = 0;
            animateConceptStep();
        }

        function animateConceptStep() {
            conceptCtx.clearRect(0, 0, conceptCanvas.width, conceptCanvas.height);
            
            // 绘制标题
            conceptCtx.fillStyle = '#333';
            conceptCtx.font = 'bold 24px Microsoft YaHei';
            conceptCtx.textAlign = 'center';
            conceptCtx.fillText('管理距离 = 路由可信度', conceptCanvas.width/2, 50);

            if (animationStep >= 1) {
                // 绘制路由器
                drawRouter(conceptCtx, 400, 150, '路由器', '#667eea');
            }

            if (animationStep >= 2) {
                // 绘制多条路径
                drawPath(conceptCtx, 200, 200, 400, 150, 'RIP (120)', '#F44336', animationStep >= 3);
                drawPath(conceptCtx, 200, 250, 400, 150, 'OSPF (110)', '#9C27B0', animationStep >= 4);
                drawPath(conceptCtx, 200, 300, 400, 150, 'EIGRP (90)', '#FF9800', animationStep >= 5);
            }

            if (animationStep >= 6) {
                // 显示选择结果
                conceptCtx.fillStyle = '#4CAF50';
                conceptCtx.font = 'bold 20px Microsoft YaHei';
                conceptCtx.fillText('✓ 选择管理距离最小的路径！', conceptCanvas.width/2, 350);
            }

            if (animationStep < 7) {
                setTimeout(() => {
                    animationStep++;
                    animateConceptStep();
                }, 1500);
            }
        }

        // 绘制路由器
        function drawRouter(ctx, x, y, label, color) {
            ctx.fillStyle = color;
            ctx.fillRect(x-40, y-20, 80, 40);
            ctx.fillStyle = 'white';
            ctx.font = '14px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText(label, x, y+5);
        }

        // 绘制路径
        function drawPath(ctx, x1, y1, x2, y2, label, color, highlight = false) {
            ctx.strokeStyle = highlight ? color : '#ddd';
            ctx.lineWidth = highlight ? 4 : 2;
            ctx.beginPath();
            ctx.moveTo(x1, y1);
            ctx.lineTo(x2, y2);
            ctx.stroke();

            // 绘制标签
            ctx.fillStyle = color;
            ctx.font = '16px Microsoft YaHei';
            ctx.textAlign = 'left';
            ctx.fillText(label, x1 - 100, y1 + 5);
        }

        // 显示管理距离表
        function showDistanceTable() {
            tableCtx.clearRect(0, 0, tableCanvas.width, tableCanvas.height);
            
            // 绘制表头
            tableCtx.fillStyle = '#667eea';
            tableCtx.fillRect(50, 50, 700, 60);
            tableCtx.fillStyle = 'white';
            tableCtx.font = 'bold 20px Microsoft YaHei';
            tableCtx.textAlign = 'center';
            tableCtx.fillText('路由协议管理距离对照表', tableCanvas.width/2, 85);

            // 绘制表格内容
            distanceData.forEach((item, index) => {
                const y = 130 + index * 60;
                
                // 绘制行背景
                tableCtx.fillStyle = index % 2 === 0 ? '#f8f9fa' : 'white';
                tableCtx.fillRect(50, y, 700, 60);
                
                // 绘制协议名称
                tableCtx.fillStyle = item.color;
                tableCtx.font = 'bold 18px Microsoft YaHei';
                tableCtx.textAlign = 'left';
                tableCtx.fillText(item.protocol, 100, y + 35);
                
                // 绘制管理距离
                tableCtx.fillStyle = '#333';
                tableCtx.font = 'bold 24px Microsoft YaHei';
                tableCtx.textAlign = 'center';
                tableCtx.fillText(item.distance.toString(), 600, y + 40);
                
                // 绘制可信度条
                const barWidth = item.distance === 0 ? 200 : Math.max(20, 200 - item.distance);
                tableCtx.fillStyle = item.color;
                tableCtx.fillRect(650, y + 20, barWidth, 20);
            });
        }

        // 高亮特定距离
        function highlightDistance(targetDistance) {
            showDistanceTable();
            
            // 添加高亮效果
            tableCtx.strokeStyle = '#FFD700';
            tableCtx.lineWidth = 4;
            tableCtx.strokeRect(45, 125, 710, 70);
            
            // 添加说明文字
            tableCtx.fillStyle = '#FFD700';
            tableCtx.font = 'bold 20px Microsoft YaHei';
            tableCtx.textAlign = 'center';
            tableCtx.fillText('管理距离15相对较小，表示路由信息比较可靠！', tableCanvas.width/2, 450);
        }

        // 模拟路由选择
        function simulateRouting() {
            routingCtx.clearRect(0, 0, routingCanvas.width, routingCanvas.height);
            
            // 绘制网络拓扑
            drawRouter(routingCtx, 100, 200, '源', '#4CAF50');
            drawRouter(routingCtx, 700, 200, '目标', '#F44336');
            
            // 绘制中间路由器
            drawRouter(routingCtx, 300, 100, 'R1', '#2196F3');
            drawRouter(routingCtx, 300, 200, 'R2', '#FF9800');
            drawRouter(routingCtx, 300, 300, 'R3', '#9C27B0');
            
            drawRouter(routingCtx, 500, 100, 'R4', '#2196F3');
            drawRouter(routingCtx, 500, 200, 'R5', '#FF9800');
            drawRouter(routingCtx, 500, 300, 'R6', '#9C27B0');

            // 绘制路径和管理距离
            setTimeout(() => {
                drawPathWithDistance(routingCtx, 140, 200, 260, 100, '静态路由 (1)', '#2196F3');
                drawPathWithDistance(routingCtx, 340, 100, 460, 100, '', '#2196F3');
                drawPathWithDistance(routingCtx, 540, 100, 660, 200, '', '#2196F3');
            }, 500);

            setTimeout(() => {
                drawPathWithDistance(routingCtx, 140, 200, 260, 200, 'EIGRP (90)', '#FF9800');
                drawPathWithDistance(routingCtx, 340, 200, 460, 200, '', '#FF9800');
                drawPathWithDistance(routingCtx, 540, 200, 660, 200, '', '#FF9800');
            }, 1000);

            setTimeout(() => {
                drawPathWithDistance(routingCtx, 140, 200, 260, 300, 'RIP (120)', '#9C27B0');
                drawPathWithDistance(routingCtx, 340, 300, 460, 300, '', '#9C27B0');
                drawPathWithDistance(routingCtx, 540, 300, 660, 200, '', '#9C27B0');
            }, 1500);

            setTimeout(() => {
                // 高亮最优路径
                routingCtx.strokeStyle = '#4CAF50';
                routingCtx.lineWidth = 6;
                routingCtx.setLineDash([10, 5]);
                routingCtx.beginPath();
                routingCtx.moveTo(140, 200);
                routingCtx.lineTo(260, 100);
                routingCtx.lineTo(340, 100);
                routingCtx.lineTo(460, 100);
                routingCtx.lineTo(540, 100);
                routingCtx.lineTo(660, 200);
                routingCtx.stroke();
                routingCtx.setLineDash([]);
                
                routingCtx.fillStyle = '#4CAF50';
                routingCtx.font = 'bold 18px Microsoft YaHei';
                routingCtx.textAlign = 'center';
                routingCtx.fillText('✓ 选择管理距离最小的路径！', routingCanvas.width/2, 50);
            }, 2000);
        }

        function drawPathWithDistance(ctx, x1, y1, x2, y2, label, color) {
            ctx.strokeStyle = color;
            ctx.lineWidth = 3;
            ctx.beginPath();
            ctx.moveTo(x1, y1);
            ctx.lineTo(x2, y2);
            ctx.stroke();

            if (label) {
                ctx.fillStyle = color;
                ctx.font = '14px Microsoft YaHei';
                ctx.textAlign = 'center';
                ctx.fillText(label, (x1 + x2) / 2, (y1 + y2) / 2 - 10);
            }
        }

        // 题目选择功能
        function selectOption(element, isCorrect) {
            const options = document.querySelectorAll('.quiz-option');
            options.forEach(opt => {
                opt.style.pointerEvents = 'none';
                if (opt === element) {
                    opt.classList.add(isCorrect ? 'correct' : 'wrong');
                } else if (!opt.classList.contains('correct')) {
                    opt.style.opacity = '0.5';
                }
            });

            // 显示正确答案
            if (!isCorrect) {
                setTimeout(() => {
                    options[2].classList.add('correct');
                }, 1000);
            }

            // 显示解析
            setTimeout(() => {
                document.getElementById('explanation').style.display = 'block';
                gsap.from('#explanation', {duration: 0.8, opacity: 0, y: 30});
                
                // 更新进度条
                gsap.to('#progressFill', {duration: 1.5, width: '100%'});
            }, 1500);
        }

        function resetAnimation() {
            conceptCtx.clearRect(0, 0, conceptCanvas.width, conceptCanvas.height);
            animationStep = 0;
        }

        function compareProtocols() {
            routingCtx.clearRect(0, 0, routingCanvas.width, routingCanvas.height);
            
            // 绘制协议比较图
            routingCtx.fillStyle = '#333';
            routingCtx.font = 'bold 20px Microsoft YaHei';
            routingCtx.textAlign = 'center';
            routingCtx.fillText('路由协议可信度对比', routingCanvas.width/2, 40);

            const protocols = [
                {name: '直连', distance: 0, x: 150},
                {name: '静态', distance: 1, x: 250},
                {name: 'EIGRP', distance: 90, x: 350},
                {name: 'OSPF', distance: 110, x: 450},
                {name: 'RIP', distance: 120, x: 550}
            ];

            protocols.forEach((protocol, index) => {
                const height = Math.max(50, 300 - protocol.distance * 2);
                const color = distanceData[index]?.color || '#666';
                
                // 绘制柱状图
                routingCtx.fillStyle = color;
                routingCtx.fillRect(protocol.x - 30, 350 - height, 60, height);
                
                // 绘制标签
                routingCtx.fillStyle = '#333';
                routingCtx.font = '14px Microsoft YaHei';
                routingCtx.textAlign = 'center';
                routingCtx.fillText(protocol.name, protocol.x, 375);
                routingCtx.fillText(`(${protocol.distance})`, protocol.x, 390);
            });

            // 绘制说明
            routingCtx.fillStyle = '#666';
            routingCtx.font = '16px Microsoft YaHei';
            routingCtx.textAlign = 'center';
            routingCtx.fillText('管理距离越小，可信度越高', routingCanvas.width/2, 80);
        }

        // 页面加载完成后初始化
        window.addEventListener('load', () => {
            showDistanceTable();
        });
    </script>
</body>
</html>
