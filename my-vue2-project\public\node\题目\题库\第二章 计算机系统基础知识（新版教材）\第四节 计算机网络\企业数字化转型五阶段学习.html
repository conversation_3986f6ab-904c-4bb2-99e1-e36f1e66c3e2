<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>企业数字化转型五阶段 - 互动学习</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/particles.js/2.0.0/particles.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            opacity: 0;
            transform: translateY(-50px);
        }

        .title {
            font-size: 3rem;
            color: white;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .subtitle {
            font-size: 1.2rem;
            color: rgba(255,255,255,0.8);
            margin-bottom: 30px;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: rgba(255,255,255,0.2);
            border-radius: 4px;
            overflow: hidden;
            margin-bottom: 40px;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4facfe 0%, #00f2fe 100%);
            width: 0%;
            transition: width 1s ease;
        }

        .stages-container {
            display: flex;
            justify-content: space-between;
            margin-bottom: 60px;
            flex-wrap: wrap;
            gap: 20px;
        }

        .stage {
            flex: 1;
            min-width: 200px;
            background: rgba(255,255,255,0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px 20px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            opacity: 0;
            transform: translateY(50px);
            border: 2px solid transparent;
        }

        .stage:hover {
            transform: translateY(-10px);
            background: rgba(255,255,255,0.2);
            border-color: rgba(255,255,255,0.3);
        }

        .stage.active {
            background: rgba(255,255,255,0.3);
            border-color: #4facfe;
            transform: scale(1.05);
        }

        .stage-number {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            font-size: 1.5rem;
            font-weight: bold;
            color: white;
            box-shadow: 0 4px 15px rgba(79, 172, 254, 0.4);
        }

        .stage-title {
            font-size: 1.1rem;
            color: white;
            margin-bottom: 10px;
            font-weight: bold;
        }

        .stage-desc {
            font-size: 0.9rem;
            color: rgba(255,255,255,0.8);
            line-height: 1.4;
        }

        .detail-panel {
            background: rgba(255,255,255,0.95);
            border-radius: 25px;
            padding: 40px;
            margin-bottom: 40px;
            opacity: 0;
            transform: translateY(30px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }

        .detail-title {
            font-size: 2rem;
            color: #333;
            margin-bottom: 30px;
            text-align: center;
        }

        .canvas-container {
            width: 100%;
            height: 300px;
            margin: 30px 0;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        #animationCanvas {
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        }

        .explanation {
            font-size: 1.1rem;
            line-height: 1.8;
            color: #555;
            margin: 20px 0;
        }

        .quiz-section {
            background: rgba(255,255,255,0.95);
            border-radius: 25px;
            padding: 40px;
            margin-top: 40px;
            opacity: 0;
            transform: translateY(30px);
        }

        .quiz-title {
            font-size: 1.8rem;
            color: #333;
            margin-bottom: 30px;
            text-align: center;
        }

        .question {
            font-size: 1.2rem;
            color: #333;
            margin-bottom: 30px;
            line-height: 1.6;
        }

        .options {
            display: grid;
            gap: 15px;
            margin-bottom: 30px;
        }

        .option {
            padding: 20px;
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 1rem;
            line-height: 1.5;
        }

        .option:hover {
            background: #e3f2fd;
            border-color: #2196f3;
        }

        .option.selected {
            background: #4facfe;
            color: white;
            border-color: #4facfe;
        }

        .option.correct {
            background: #4caf50;
            color: white;
            border-color: #4caf50;
        }

        .option.wrong {
            background: #f44336;
            color: white;
            border-color: #f44336;
        }

        .submit-btn {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            border: none;
            padding: 15px 40px;
            border-radius: 25px;
            font-size: 1.1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            display: block;
            margin: 0 auto;
        }

        .submit-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(79, 172, 254, 0.3);
        }

        .result {
            margin-top: 30px;
            padding: 20px;
            border-radius: 15px;
            text-align: center;
            font-size: 1.1rem;
            opacity: 0;
        }

        .result.correct {
            background: #e8f5e8;
            color: #2e7d32;
            border: 2px solid #4caf50;
        }

        .result.wrong {
            background: #ffebee;
            color: #c62828;
            border: 2px solid #f44336;
        }

        .particles-js {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
        }

        @media (max-width: 768px) {
            .stages-container {
                flex-direction: column;
            }
            
            .title {
                font-size: 2rem;
            }
            
            .container {
                padding: 20px 10px;
            }
        }
    </style>
</head>
<body>
    <div id="particles-js" class="particles-js"></div>
    
    <div class="container">
        <div class="header">
            <h1 class="title">企业数字化转型五阶段</h1>
            <p class="subtitle">从零开始，轻松掌握数字化转型的发展历程</p>
            <div class="progress-bar">
                <div class="progress-fill"></div>
            </div>
        </div>

        <div class="stages-container">
            <div class="stage" data-stage="1">
                <div class="stage-number">1</div>
                <div class="stage-title">初始级</div>
                <div class="stage-desc">传统纸质办公，手工操作为主</div>
            </div>
            <div class="stage" data-stage="2">
                <div class="stage-number">2</div>
                <div class="stage-title">单元级</div>
                <div class="stage-desc">部分部门开始使用电脑</div>
            </div>
            <div class="stage" data-stage="3">
                <div class="stage-number">3</div>
                <div class="stage-title">流程级</div>
                <div class="stage-desc">业务流程数字化改造</div>
            </div>
            <div class="stage" data-stage="4">
                <div class="stage-number">4</div>
                <div class="stage-title">网络级</div>
                <div class="stage-desc">系统互联，数据共享</div>
            </div>
            <div class="stage" data-stage="5">
                <div class="stage-number">5</div>
                <div class="stage-title">生态级</div>
                <div class="stage-desc">全面智能化生态系统</div>
            </div>
        </div>

        <div class="detail-panel">
            <h2 class="detail-title">点击上方阶段，查看详细解释</h2>
            <div class="canvas-container">
                <canvas id="animationCanvas"></canvas>
            </div>
            <div class="explanation" id="explanation">
                选择一个阶段开始学习吧！每个阶段都有生动的动画演示，帮助你理解企业数字化转型的过程。
            </div>
        </div>

        <div class="quiz-section">
            <h2 class="quiz-title">📝 练习题目</h2>
            <div class="question">
                企业数字化转型的五个发展阶段依次是（ ）
            </div>
            <div class="options">
                <div class="option" data-option="A">
                    A. 初始级发展阶段、单元级发展阶段、流程级发展阶段、网络级发展阶段、生态级发展阶段
                </div>
                <div class="option" data-option="B">
                    B. 初始级发展阶段、单元级发展阶段、系统级发展阶段、网络级发展阶段、生态级发展阶段
                </div>
                <div class="option" data-option="C">
                    C. 初始级发展阶段、单元级发展阶段、流程级发展阶段、网络服发展阶段、优化级发展阶段
                </div>
                <div class="option" data-option="D">
                    D. 初始级发展阶段、流程级发展阶段、系统级发展阶段、网络级发展阶段、生态级发展阶段
                </div>
            </div>
            <button class="submit-btn" onclick="checkAnswer()">提交答案</button>
            <div class="result" id="result"></div>
        </div>
    </div>

    <script>
        // 粒子背景效果
        particlesJS('particles-js', {
            particles: {
                number: { value: 80, density: { enable: true, value_area: 800 } },
                color: { value: "#ffffff" },
                shape: { type: "circle" },
                opacity: { value: 0.5, random: false },
                size: { value: 3, random: true },
                line_linked: { enable: true, distance: 150, color: "#ffffff", opacity: 0.4, width: 1 },
                move: { enable: true, speed: 6, direction: "none", random: false, straight: false, out_mode: "out", bounce: false }
            },
            interactivity: {
                detect_on: "canvas",
                events: { onhover: { enable: true, mode: "repulse" }, onclick: { enable: true, mode: "push" }, resize: true },
                modes: { grab: { distance: 400, line_linked: { opacity: 1 } }, bubble: { distance: 400, size: 40, duration: 2, opacity: 8, speed: 3 }, repulse: { distance: 200, duration: 0.4 }, push: { particles_nb: 4 }, remove: { particles_nb: 2 } }
            },
            retina_detect: true
        });

        // 页面加载动画
        window.addEventListener('load', function() {
            gsap.to('.header', { duration: 1, opacity: 1, y: 0, ease: "power2.out" });
            gsap.to('.stage', { duration: 0.8, opacity: 1, y: 0, stagger: 0.2, delay: 0.5, ease: "power2.out" });
            gsap.to('.detail-panel', { duration: 1, opacity: 1, y: 0, delay: 1, ease: "power2.out" });
            gsap.to('.quiz-section', { duration: 1, opacity: 1, y: 0, delay: 1.5, ease: "power2.out" });
        });

        // Canvas 动画
        const canvas = document.getElementById('animationCanvas');
        const ctx = canvas.getContext('2d');
        
        function resizeCanvas() {
            canvas.width = canvas.offsetWidth;
            canvas.height = canvas.offsetHeight;
        }
        resizeCanvas();
        window.addEventListener('resize', resizeCanvas);

        let currentStage = 0;
        let animationId;

        // 阶段数据
        const stageData = {
            1: {
                title: "初始级发展阶段",
                description: "这是企业数字化的起点。企业主要依靠传统的纸质文档和手工操作。就像古代的商人用算盘记账一样，效率低但是基础扎实。",
                animation: drawInitialStage
            },
            2: {
                title: "单元级发展阶段", 
                description: "企业开始在个别部门引入计算机。就像在传统店铺里放了一台收银机，虽然只是局部改进，但已经开始尝试数字化工具。",
                animation: drawUnitStage
            },
            3: {
                title: "流程级发展阶段",
                description: "企业开始将整个业务流程进行数字化改造。就像把整个生产线都装上了传送带，各个环节开始连接起来，效率大大提升。",
                animation: drawProcessStage
            },
            4: {
                title: "网络级发展阶段",
                description: "各个系统开始互相连接，数据可以在不同部门之间共享。就像把各个房间都连上了网络，信息可以快速传递。",
                animation: drawNetworkStage
            },
            5: {
                title: "生态级发展阶段",
                description: "企业形成完整的数字化生态系统，具备人工智能、大数据分析等高级功能。就像一个智慧城市，所有系统协调运作。",
                animation: drawEcosystemStage
            }
        };

        // 阶段点击事件
        document.querySelectorAll('.stage').forEach(stage => {
            stage.addEventListener('click', function() {
                const stageNum = parseInt(this.dataset.stage);
                selectStage(stageNum);
            });
        });

        function selectStage(stageNum) {
            // 更新活跃状态
            document.querySelectorAll('.stage').forEach(s => s.classList.remove('active'));
            document.querySelector(`[data-stage="${stageNum}"]`).classList.add('active');
            
            // 更新进度条
            const progress = (stageNum / 5) * 100;
            document.querySelector('.progress-fill').style.width = progress + '%';
            
            // 更新详细信息
            const data = stageData[stageNum];
            document.querySelector('.detail-title').textContent = data.title;
            document.getElementById('explanation').textContent = data.description;
            
            // 开始动画
            currentStage = stageNum;
            if (animationId) cancelAnimationFrame(animationId);
            data.animation();
        }

        // 动画函数
        function drawInitialStage() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            const time = Date.now() * 0.002;
            
            // 绘制纸质文档
            for (let i = 0; i < 5; i++) {
                const x = 100 + i * 120;
                const y = 150 + Math.sin(time + i) * 10;
                
                ctx.fillStyle = '#fff';
                ctx.fillRect(x, y, 80, 100);
                ctx.strokeStyle = '#333';
                ctx.lineWidth = 2;
                ctx.strokeRect(x, y, 80, 100);
                
                // 绘制文字线条
                ctx.strokeStyle = '#666';
                ctx.lineWidth = 1;
                for (let j = 0; j < 5; j++) {
                    ctx.beginPath();
                    ctx.moveTo(x + 10, y + 20 + j * 15);
                    ctx.lineTo(x + 70, y + 20 + j * 15);
                    ctx.stroke();
                }
            }
            
            // 绘制手工操作的手
            const handX = 300 + Math.sin(time * 2) * 20;
            const handY = 100;
            
            ctx.fillStyle = '#fdbcb4';
            ctx.beginPath();
            ctx.arc(handX, handY, 15, 0, Math.PI * 2);
            ctx.fill();
            
            // 绘制笔
            ctx.strokeStyle = '#333';
            ctx.lineWidth = 3;
            ctx.beginPath();
            ctx.moveTo(handX, handY);
            ctx.lineTo(handX + 30, handY - 30);
            ctx.stroke();
            
            animationId = requestAnimationFrame(drawInitialStage);
        }

        function drawUnitStage() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            const time = Date.now() * 0.003;
            
            // 绘制传统办公区域
            ctx.fillStyle = '#f0f0f0';
            ctx.fillRect(50, 100, 200, 150);
            ctx.strokeStyle = '#333';
            ctx.lineWidth = 2;
            ctx.strokeRect(50, 100, 200, 150);
            
            // 绘制电脑
            const computerX = 350;
            const computerY = 150;
            
            // 电脑屏幕
            ctx.fillStyle = '#333';
            ctx.fillRect(computerX, computerY, 120, 80);
            ctx.fillStyle = '#4facfe';
            ctx.fillRect(computerX + 10, computerY + 10, 100, 60);
            
            // 闪烁效果
            if (Math.sin(time * 3) > 0) {
                ctx.fillStyle = 'rgba(79, 172, 254, 0.5)';
                ctx.fillRect(computerX + 10, computerY + 10, 100, 60);
            }
            
            // 键盘
            ctx.fillStyle = '#666';
            ctx.fillRect(computerX + 10, computerY + 90, 100, 30);
            
            // 连接线动画
            const lineProgress = (Math.sin(time) + 1) / 2;
            ctx.strokeStyle = '#4facfe';
            ctx.lineWidth = 3;
            ctx.beginPath();
            ctx.moveTo(250, 175);
            ctx.lineTo(250 + (100 * lineProgress), 175);
            ctx.stroke();
            
            animationId = requestAnimationFrame(drawUnitStage);
        }

        function drawProcessStage() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            const time = Date.now() * 0.002;
            
            // 绘制流程步骤
            const steps = ['输入', '处理', '输出'];
            const stepWidth = 100;
            const stepHeight = 60;
            
            for (let i = 0; i < steps.length; i++) {
                const x = 100 + i * 150;
                const y = 120;
                
                // 步骤框
                ctx.fillStyle = i === Math.floor(time * 2) % 3 ? '#4facfe' : '#e0e0e0';
                ctx.fillRect(x, y, stepWidth, stepHeight);
                ctx.strokeStyle = '#333';
                ctx.lineWidth = 2;
                ctx.strokeRect(x, y, stepWidth, stepHeight);
                
                // 步骤文字
                ctx.fillStyle = '#333';
                ctx.font = '16px Microsoft YaHei';
                ctx.textAlign = 'center';
                ctx.fillText(steps[i], x + stepWidth/2, y + stepHeight/2 + 5);
                
                // 箭头
                if (i < steps.length - 1) {
                    const arrowX = x + stepWidth + 10;
                    const arrowY = y + stepHeight/2;
                    
                    ctx.strokeStyle = '#4facfe';
                    ctx.lineWidth = 3;
                    ctx.beginPath();
                    ctx.moveTo(arrowX, arrowY);
                    ctx.lineTo(arrowX + 30, arrowY);
                    ctx.moveTo(arrowX + 25, arrowY - 5);
                    ctx.lineTo(arrowX + 30, arrowY);
                    ctx.lineTo(arrowX + 25, arrowY + 5);
                    ctx.stroke();
                }
            }
            
            // 数据流动效果
            const flowY = 200;
            for (let i = 0; i < 10; i++) {
                const x = (time * 100 + i * 50) % (canvas.width + 50);
                ctx.fillStyle = `hsl(${200 + i * 20}, 70%, 60%)`;
                ctx.beginPath();
                ctx.arc(x, flowY, 5, 0, Math.PI * 2);
                ctx.fill();
            }
            
            animationId = requestAnimationFrame(drawProcessStage);
        }

        function drawNetworkStage() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            const time = Date.now() * 0.001;
            
            // 网络节点
            const nodes = [
                {x: 150, y: 100, label: '销售'},
                {x: 350, y: 80, label: '生产'},
                {x: 550, y: 120, label: '财务'},
                {x: 250, y: 200, label: '人事'},
                {x: 450, y: 220, label: '物流'}
            ];
            
            // 绘制连接线
            ctx.strokeStyle = '#4facfe';
            ctx.lineWidth = 2;
            for (let i = 0; i < nodes.length; i++) {
                for (let j = i + 1; j < nodes.length; j++) {
                    const alpha = (Math.sin(time * 2 + i + j) + 1) / 2;
                    ctx.globalAlpha = alpha * 0.8 + 0.2;
                    
                    ctx.beginPath();
                    ctx.moveTo(nodes[i].x, nodes[i].y);
                    ctx.lineTo(nodes[j].x, nodes[j].y);
                    ctx.stroke();
                }
            }
            
            ctx.globalAlpha = 1;
            
            // 绘制节点
            nodes.forEach((node, index) => {
                const pulse = Math.sin(time * 3 + index) * 5 + 25;
                
                ctx.fillStyle = '#4facfe';
                ctx.beginPath();
                ctx.arc(node.x, node.y, pulse, 0, Math.PI * 2);
                ctx.fill();
                
                ctx.fillStyle = '#fff';
                ctx.beginPath();
                ctx.arc(node.x, node.y, 20, 0, Math.PI * 2);
                ctx.fill();
                
                ctx.fillStyle = '#333';
                ctx.font = '12px Microsoft YaHei';
                ctx.textAlign = 'center';
                ctx.fillText(node.label, node.x, node.y + 4);
            });
            
            // 数据包传输
            for (let i = 0; i < 3; i++) {
                const progress = (time + i * 0.5) % 1;
                const startNode = nodes[i % nodes.length];
                const endNode = nodes[(i + 1) % nodes.length];
                
                const x = startNode.x + (endNode.x - startNode.x) * progress;
                const y = startNode.y + (endNode.y - startNode.y) * progress;
                
                ctx.fillStyle = '#ff6b6b';
                ctx.beginPath();
                ctx.arc(x, y, 8, 0, Math.PI * 2);
                ctx.fill();
            }
            
            animationId = requestAnimationFrame(drawNetworkStage);
        }

        function drawEcosystemStage() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            const time = Date.now() * 0.001;
            
            // 中心AI大脑
            const centerX = canvas.width / 2;
            const centerY = canvas.height / 2;
            
            // AI大脑光环
            for (let i = 0; i < 3; i++) {
                const radius = 40 + i * 20 + Math.sin(time * 2 + i) * 5;
                ctx.strokeStyle = `rgba(79, 172, 254, ${0.8 - i * 0.2})`;
                ctx.lineWidth = 3;
                ctx.beginPath();
                ctx.arc(centerX, centerY, radius, 0, Math.PI * 2);
                ctx.stroke();
            }
            
            // AI大脑核心
            ctx.fillStyle = '#4facfe';
            ctx.beginPath();
            ctx.arc(centerX, centerY, 30, 0, Math.PI * 2);
            ctx.fill();
            
            ctx.fillStyle = '#fff';
            ctx.font = '16px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('AI', centerX, centerY + 5);
            
            // 周围的智能模块
            const modules = [
                {angle: 0, label: '智能分析', color: '#ff6b6b'},
                {angle: Math.PI/3, label: '自动化', color: '#4ecdc4'},
                {angle: Math.PI*2/3, label: '预测', color: '#45b7d1'},
                {angle: Math.PI, label: '优化', color: '#96ceb4'},
                {angle: Math.PI*4/3, label: '学习', color: '#feca57'},
                {angle: Math.PI*5/3, label: '决策', color: '#ff9ff3'}
            ];
            
            modules.forEach((module, index) => {
                const radius = 120;
                const x = centerX + Math.cos(module.angle + time * 0.5) * radius;
                const y = centerY + Math.sin(module.angle + time * 0.5) * radius;
                
                // 连接线
                ctx.strokeStyle = module.color;
                ctx.lineWidth = 2;
                ctx.globalAlpha = 0.6;
                ctx.beginPath();
                ctx.moveTo(centerX, centerY);
                ctx.lineTo(x, y);
                ctx.stroke();
                
                ctx.globalAlpha = 1;
                
                // 模块圆圈
                ctx.fillStyle = module.color;
                ctx.beginPath();
                ctx.arc(x, y, 25, 0, Math.PI * 2);
                ctx.fill();
                
                ctx.fillStyle = '#fff';
                ctx.font = '10px Microsoft YaHei';
                ctx.textAlign = 'center';
                ctx.fillText(module.label, x, y + 3);
                
                // 数据流动
                const flowProgress = (time * 2 + index) % 1;
                const flowX = centerX + (x - centerX) * flowProgress;
                const flowY = centerY + (y - centerY) * flowProgress;
                
                ctx.fillStyle = 'rgba(255, 255, 255, 0.8)';
                ctx.beginPath();
                ctx.arc(flowX, flowY, 3, 0, Math.PI * 2);
                ctx.fill();
            });
            
            animationId = requestAnimationFrame(drawEcosystemStage);
        }

        // 默认显示第一阶段
        selectStage(1);

        // 题目相关
        let selectedOption = null;
        let answered = false;

        document.querySelectorAll('.option').forEach(option => {
            option.addEventListener('click', function() {
                if (answered) return;
                
                document.querySelectorAll('.option').forEach(opt => opt.classList.remove('selected'));
                this.classList.add('selected');
                selectedOption = this.dataset.option;
            });
        });

        function checkAnswer() {
            if (!selectedOption || answered) return;
            
            answered = true;
            const correctAnswer = 'A';
            const resultDiv = document.getElementById('result');
            
            document.querySelectorAll('.option').forEach(option => {
                const optionLetter = option.dataset.option;
                if (optionLetter === correctAnswer) {
                    option.classList.add('correct');
                } else if (optionLetter === selectedOption && selectedOption !== correctAnswer) {
                    option.classList.add('wrong');
                }
            });
            
            if (selectedOption === correctAnswer) {
                resultDiv.className = 'result correct';
                resultDiv.innerHTML = `
                    <h3>🎉 恭喜答对了！</h3>
                    <p><strong>正确答案：A</strong></p>
                    <p><strong>解题思路：</strong></p>
                    <p>1. 记住关键词：<strong>初始级 → 单元级 → 流程级 → 网络级 → 生态级</strong></p>
                    <p>2. 理解发展逻辑：从<strong>无数字化</strong>到<strong>局部数字化</strong>，再到<strong>流程数字化</strong>，然后<strong>系统互联</strong>，最后形成<strong>智能生态</strong></p>
                    <p>3. 注意区分：选项B中的"系统级"不是标准阶段名称，选项C和D的顺序或名称都有错误</p>
                `;
            } else {
                resultDiv.className = 'result wrong';
                resultDiv.innerHTML = `
                    <h3>❌ 答案不正确</h3>
                    <p><strong>正确答案：A</strong></p>
                    <p><strong>你的答案：${selectedOption}</strong></p>
                    <p><strong>错误分析：</strong></p>
                    <p>请重新学习上面的五个阶段，注意每个阶段的准确名称和发展顺序。记住口诀：<strong>"初单流网生"</strong>（初始级、单元级、流程级、网络级、生态级）</p>
                `;
            }
            
            gsap.to(resultDiv, { duration: 0.5, opacity: 1, ease: "power2.out" });
        }
    </script>
</body>
</html>
