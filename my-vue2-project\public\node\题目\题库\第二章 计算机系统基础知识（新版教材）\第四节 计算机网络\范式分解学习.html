<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据库范式分解学习工具</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
        }

        .section {
            margin-bottom: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 15px;
            border-left: 5px solid #667eea;
        }

        .section h2 {
            color: #667eea;
            margin-bottom: 15px;
            font-size: 1.5em;
        }

        .table-container {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin: 15px 0;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin: 10px 0;
        }

        th, td {
            border: 2px solid #ddd;
            padding: 12px;
            text-align: center;
            transition: all 0.3s ease;
        }

        th {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            font-weight: bold;
        }

        td {
            background: #fff;
        }

        tr:hover td {
            background: #f0f8ff;
            transform: scale(1.02);
        }

        .controls {
            display: flex;
            gap: 15px;
            margin: 20px 0;
            flex-wrap: wrap;
            justify-content: center;
        }

        button {
            padding: 12px 24px;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
        }

        .btn-secondary {
            background: linear-gradient(135deg, #ffeaa7, #fab1a0);
            color: #333;
        }

        .btn-success {
            background: linear-gradient(135deg, #00b894, #00cec9);
            color: white;
        }

        button:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
        }

        .step-indicator {
            display: flex;
            justify-content: center;
            margin: 20px 0;
            gap: 20px;
        }

        .step {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            color: white;
            transition: all 0.3s ease;
        }

        .step.active {
            background: linear-gradient(135deg, #667eea, #764ba2);
            transform: scale(1.2);
        }

        .step.completed {
            background: linear-gradient(135deg, #00b894, #00cec9);
        }

        .step.inactive {
            background: #ccc;
        }

        .explanation {
            background: #e8f4fd;
            border-radius: 10px;
            padding: 15px;
            margin: 15px 0;
            border-left: 4px solid #667eea;
        }

        .highlight {
            background: #fff3cd !important;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        .dependency-list {
            background: white;
            border-radius: 10px;
            padding: 15px;
            margin: 10px 0;
        }

        .dependency-item {
            padding: 8px 15px;
            margin: 5px 0;
            background: #f8f9fa;
            border-radius: 20px;
            border-left: 4px solid #667eea;
            transition: all 0.3s ease;
        }

        .dependency-item:hover {
            background: #e3f2fd;
            transform: translateX(10px);
        }

        @media (max-width: 768px) {
            .container {
                padding: 15px;
            }
            
            h1 {
                font-size: 2em;
            }
            
            .controls {
                flex-direction: column;
                align-items: center;
            }
            
            button {
                width: 100%;
                max-width: 300px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎓 数据库范式分解学习工具</h1>
        
        <div class="step-indicator">
            <div class="step active" id="step1">1</div>
            <div class="step inactive" id="step2">2</div>
            <div class="step inactive" id="step3">3</div>
            <div class="step inactive" id="step4">4</div>
        </div>

        <div class="section">
            <h2>📊 原始关系表</h2>
            <div class="table-container">
                <table id="originalTable">
                    <thead>
                        <tr>
                            <th>学号</th>
                            <th>姓名</th>
                            <th>课程号</th>
                            <th>课程名</th>
                            <th>成绩</th>
                            <th>教师</th>
                            <th>系别</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>001</td>
                            <td>张三</td>
                            <td>C001</td>
                            <td>数据库</td>
                            <td>85</td>
                            <td>李老师</td>
                            <td>计算机系</td>
                        </tr>
                        <tr>
                            <td>001</td>
                            <td>张三</td>
                            <td>C002</td>
                            <td>数据结构</td>
                            <td>90</td>
                            <td>王老师</td>
                            <td>计算机系</td>
                        </tr>
                        <tr>
                            <td>002</td>
                            <td>李四</td>
                            <td>C001</td>
                            <td>数据库</td>
                            <td>78</td>
                            <td>李老师</td>
                            <td>软件工程系</td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <div class="explanation">
                <strong>问题分析：</strong>这个表存在数据冗余和更新异常问题。让我们通过范式分解来解决！
            </div>
        </div>

        <div class="controls">
            <button class="btn-primary" onclick="showDependencies()">🔍 分析函数依赖</button>
            <button class="btn-secondary" onclick="decompose1NF()">📋 第一范式(1NF)</button>
            <button class="btn-secondary" onclick="decompose2NF()">🔄 第二范式(2NF)</button>
            <button class="btn-success" onclick="decompose3NF()">✨ 第三范式(3NF)</button>
            <button class="btn-primary" onclick="reset()">🔄 重置</button>
        </div>

        <div class="section" id="dependencySection" style="display: none;">
            <h2>🔗 函数依赖分析</h2>
            <div class="dependency-list">
                <div class="dependency-item">学号 → 姓名, 系别</div>
                <div class="dependency-item">课程号 → 课程名, 教师</div>
                <div class="dependency-item">(学号, 课程号) → 成绩</div>
            </div>
            <div class="explanation">
                <strong>主键：</strong>(学号, 课程号) - 复合主键<br>
                <strong>问题：</strong>存在部分函数依赖，违反了2NF
            </div>
        </div>

        <div class="section" id="result1NF" style="display: none;">
            <h2>📋 第一范式 (1NF)</h2>
            <div class="explanation">
                <strong>1NF要求：</strong>每个属性都是原子性的，不可再分。<br>
                当前表已满足1NF要求，所有字段都是原子值。
            </div>
        </div>

        <div class="section" id="result2NF" style="display: none;">
            <h2>🔄 第二范式 (2NF) 分解结果</h2>
            
            <div class="table-container">
                <h3>学生表 (Students)</h3>
                <table>
                    <thead>
                        <tr><th>学号</th><th>姓名</th><th>系别</th></tr>
                    </thead>
                    <tbody>
                        <tr><td>001</td><td>张三</td><td>计算机系</td></tr>
                        <tr><td>002</td><td>李四</td><td>软件工程系</td></tr>
                    </tbody>
                </table>
            </div>

            <div class="table-container">
                <h3>课程表 (Courses)</h3>
                <table>
                    <thead>
                        <tr><th>课程号</th><th>课程名</th><th>教师</th></tr>
                    </thead>
                    <tbody>
                        <tr><td>C001</td><td>数据库</td><td>李老师</td></tr>
                        <tr><td>C002</td><td>数据结构</td><td>王老师</td></tr>
                    </tbody>
                </table>
            </div>

            <div class="table-container">
                <h3>选课表 (Enrollments)</h3>
                <table>
                    <thead>
                        <tr><th>学号</th><th>课程号</th><th>成绩</th></tr>
                    </thead>
                    <tbody>
                        <tr><td>001</td><td>C001</td><td>85</td></tr>
                        <tr><td>001</td><td>C002</td><td>90</td></tr>
                        <tr><td>002</td><td>C001</td><td>78</td></tr>
                    </tbody>
                </table>
            </div>

            <div class="explanation">
                <strong>2NF改进：</strong>消除了部分函数依赖，减少了数据冗余。但仍存在传递函数依赖问题。
            </div>
        </div>

        <div class="section" id="result3NF" style="display: none;">
            <h2>✨ 第三范式 (3NF) 最终分解结果</h2>
            
            <div class="table-container">
                <h3>学生表 (Students)</h3>
                <table>
                    <thead>
                        <tr><th>学号</th><th>姓名</th><th>系别ID</th></tr>
                    </thead>
                    <tbody>
                        <tr><td>001</td><td>张三</td><td>D001</td></tr>
                        <tr><td>002</td><td>李四</td><td>D002</td></tr>
                    </tbody>
                </table>
            </div>

            <div class="table-container">
                <h3>系别表 (Departments)</h3>
                <table>
                    <thead>
                        <tr><th>系别ID</th><th>系别名称</th></tr>
                    </thead>
                    <tbody>
                        <tr><td>D001</td><td>计算机系</td></tr>
                        <tr><td>D002</td><td>软件工程系</td></tr>
                    </tbody>
                </table>
            </div>

            <div class="table-container">
                <h3>课程表 (Courses)</h3>
                <table>
                    <thead>
                        <tr><th>课程号</th><th>课程名</th><th>教师</th></tr>
                    </thead>
                    <tbody>
                        <tr><td>C001</td><td>数据库</td><td>李老师</td></tr>
                        <tr><td>C002</td><td>数据结构</td><td>王老师</td></tr>
                    </tbody>
                </table>
            </div>

            <div class="table-container">
                <h3>选课表 (Enrollments)</h3>
                <table>
                    <thead>
                        <tr><th>学号</th><th>课程号</th><th>成绩</th></tr>
                    </thead>
                    <tbody>
                        <tr><td>001</td><td>C001</td><td>85</td></tr>
                        <tr><td>001</td><td>C002</td><td>90</td></tr>
                        <tr><td>002</td><td>C001</td><td>78</td></tr>
                    </tbody>
                </table>
            </div>

            <div class="explanation">
                <strong>3NF优势：</strong>
                <ul style="margin-top: 10px; padding-left: 20px;">
                    <li>✅ 消除了数据冗余</li>
                    <li>✅ 避免了更新异常</li>
                    <li>✅ 减少了存储空间</li>
                    <li>✅ 提高了数据一致性</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        let currentStep = 1;

        function updateStepIndicator(step) {
            // 重置所有步骤
            for (let i = 1; i <= 4; i++) {
                const stepElement = document.getElementById(`step${i}`);
                stepElement.className = 'step';
                if (i < step) {
                    stepElement.className += ' completed';
                } else if (i === step) {
                    stepElement.className += ' active';
                } else {
                    stepElement.className += ' inactive';
                }
            }
            currentStep = step;
        }

        function showDependencies() {
            document.getElementById('dependencySection').style.display = 'block';
            updateStepIndicator(2);
            
            // 高亮原始表中的相关字段
            const table = document.getElementById('originalTable');
            const rows = table.querySelectorAll('tbody tr');
            rows.forEach(row => {
                row.classList.add('highlight');
            });
            
            setTimeout(() => {
                rows.forEach(row => {
                    row.classList.remove('highlight');
                });
            }, 3000);
        }

        function decompose1NF() {
            document.getElementById('result1NF').style.display = 'block';
            updateStepIndicator(2);
        }

        function decompose2NF() {
            document.getElementById('result2NF').style.display = 'block';
            updateStepIndicator(3);
            
            // 添加动画效果
            const tables = document.querySelectorAll('#result2NF table');
            tables.forEach((table, index) => {
                setTimeout(() => {
                    table.style.opacity = '0';
                    table.style.transform = 'translateY(20px)';
                    table.style.transition = 'all 0.5s ease';
                    setTimeout(() => {
                        table.style.opacity = '1';
                        table.style.transform = 'translateY(0)';
                    }, 100);
                }, index * 200);
            });
        }

        function decompose3NF() {
            document.getElementById('result3NF').style.display = 'block';
            updateStepIndicator(4);
            
            // 添加成功动画
            const section = document.getElementById('result3NF');
            section.style.animation = 'pulse 1s ease-in-out';
            
            // 添加表格动画
            const tables = document.querySelectorAll('#result3NF table');
            tables.forEach((table, index) => {
                setTimeout(() => {
                    table.style.opacity = '0';
                    table.style.transform = 'scale(0.8)';
                    table.style.transition = 'all 0.6s ease';
                    setTimeout(() => {
                        table.style.opacity = '1';
                        table.style.transform = 'scale(1)';
                    }, 100);
                }, index * 300);
            });
        }

        function reset() {
            // 隐藏所有结果部分
            document.getElementById('dependencySection').style.display = 'none';
            document.getElementById('result1NF').style.display = 'none';
            document.getElementById('result2NF').style.display = 'none';
            document.getElementById('result3NF').style.display = 'none';
            
            // 重置步骤指示器
            updateStepIndicator(1);
            
            // 移除高亮效果
            const highlightedRows = document.querySelectorAll('.highlight');
            highlightedRows.forEach(row => {
                row.classList.remove('highlight');
            });
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('范式分解学习工具已加载完成！');
            
            // 添加表格悬停效果
            const tables = document.querySelectorAll('table');
            tables.forEach(table => {
                const rows = table.querySelectorAll('tbody tr');
                rows.forEach(row => {
                    row.addEventListener('mouseenter', function() {
                        this.style.transform = 'scale(1.02)';
                        this.style.boxShadow = '0 5px 15px rgba(0,0,0,0.1)';
                    });
                    
                    row.addEventListener('mouseleave', function() {
                        this.style.transform = 'scale(1)';
                        this.style.boxShadow = 'none';
                    });
                });
            });
        });
    </script>
</body>
</html>
