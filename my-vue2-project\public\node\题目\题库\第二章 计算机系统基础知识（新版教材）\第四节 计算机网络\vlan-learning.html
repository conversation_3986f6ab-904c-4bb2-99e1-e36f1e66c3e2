<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VLAN 虚拟局域网 - 互动学习</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            padding: 40px 0;
        }

        .title {
            font-size: 3rem;
            color: white;
            margin-bottom: 20px;
            text-shadow: 0 4px 8px rgba(0,0,0,0.3);
            opacity: 0;
            transform: translateY(-50px);
        }

        .subtitle {
            font-size: 1.2rem;
            color: rgba(255,255,255,0.9);
            opacity: 0;
            transform: translateY(30px);
        }

        .section {
            background: rgba(255,255,255,0.95);
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            opacity: 0;
            transform: translateY(50px);
        }

        .section-title {
            font-size: 2rem;
            color: #333;
            margin-bottom: 30px;
            text-align: center;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        .network-demo {
            position: relative;
            height: 400px;
            margin: 40px 0;
            background: #f8f9fa;
            border-radius: 15px;
            overflow: hidden;
        }

        .device {
            position: absolute;
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        }

        .device:hover {
            transform: scale(1.1);
            box-shadow: 0 6px 20px rgba(0,0,0,0.3);
        }

        .computer {
            background: #4CAF50;
        }

        .switch {
            background: #2196F3;
            width: 80px;
            height: 40px;
            border-radius: 10px;
        }

        .router {
            background: #FF9800;
            width: 70px;
            height: 50px;
            border-radius: 8px;
        }

        .connection {
            position: absolute;
            height: 2px;
            background: #666;
            transform-origin: left center;
            opacity: 0.7;
        }

        .broadcast-wave {
            position: absolute;
            border: 2px solid #ff4444;
            border-radius: 50%;
            opacity: 0;
        }

        .vlan-group {
            position: absolute;
            border: 3px dashed;
            border-radius: 15px;
            background: rgba(255,255,255,0.1);
            opacity: 0;
        }

        .vlan1 { border-color: #ff6b6b; background: rgba(255,107,107,0.1); }
        .vlan2 { border-color: #4ecdc4; background: rgba(78,205,196,0.1); }
        .vlan3 { border-color: #45b7d1; background: rgba(69,183,209,0.1); }

        .control-panel {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin: 30px 0;
            flex-wrap: wrap;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 25px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.3);
        }

        .btn:active {
            transform: translateY(0);
        }

        .explanation {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 15px;
            margin: 20px 0;
            border-left: 5px solid #667eea;
        }

        .quiz-section {
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            color: white;
            text-align: center;
        }

        .quiz-question {
            font-size: 1.3rem;
            margin-bottom: 30px;
            line-height: 1.6;
        }

        .quiz-options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin: 30px 0;
        }

        .quiz-option {
            padding: 15px 20px;
            background: rgba(255,255,255,0.2);
            border: 2px solid transparent;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(5px);
        }

        .quiz-option:hover {
            background: rgba(255,255,255,0.3);
            transform: translateY(-2px);
        }

        .quiz-option.correct {
            border-color: #4CAF50;
            background: rgba(76,175,80,0.3);
        }

        .quiz-option.wrong {
            border-color: #f44336;
            background: rgba(244,67,54,0.3);
        }

        .progress-bar {
            width: 100%;
            height: 6px;
            background: rgba(255,255,255,0.3);
            border-radius: 3px;
            margin: 20px 0;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4CAF50, #8BC34A);
            width: 0%;
            transition: width 0.5s ease;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .pulse {
            animation: pulse 2s infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }

        .float {
            animation: float 3s ease-in-out infinite;
        }

        .canvas-container {
            position: relative;
            width: 100%;
            height: 300px;
            background: #f8f9fa;
            border-radius: 15px;
            overflow: hidden;
            margin: 20px 0;
        }

        #networkCanvas {
            width: 100%;
            height: 100%;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">VLAN 虚拟局域网</h1>
            <p class="subtitle">让我们用动画来理解网络的神奇世界！</p>
        </div>

        <div class="section">
            <h2 class="section-title">🌐 什么是网络？</h2>
            <div class="explanation">
                <p>想象一下，网络就像一个大城市，计算机就是城市里的房子，它们需要通过道路（网线）来互相通信。</p>
            </div>
            <div class="canvas-container">
                <canvas id="networkCanvas"></canvas>
            </div>
            <div class="control-panel">
                <button class="btn" onclick="showBasicNetwork()">显示基础网络</button>
                <button class="btn" onclick="showBroadcastStorm()">广播风暴演示</button>
            </div>
        </div>

        <div class="section">
            <h2 class="section-title">🔧 交换机的作用</h2>
            <div class="explanation">
                <p>交换机就像城市的交通枢纽，它连接多台计算机，让它们能够互相通信。但是有个问题...</p>
            </div>
            <div class="network-demo" id="switchDemo">
                <!-- 动态生成网络设备 -->
            </div>
            <div class="control-panel">
                <button class="btn" onclick="demonstrateSwitch()">交换机工作原理</button>
                <button class="btn" onclick="showBroadcastProblem()">广播问题演示</button>
            </div>
        </div>

        <div class="section">
            <h2 class="section-title">✨ VLAN 的神奇魔法</h2>
            <div class="explanation">
                <p>VLAN就像给网络施了魔法！它可以把一个物理交换机变成多个虚拟的小网络，每个小网络都是独立的。</p>
            </div>
            <div class="network-demo" id="vlanDemo">
                <!-- VLAN演示区域 -->
            </div>
            <div class="control-panel">
                <button class="btn" onclick="showVLANMagic()">VLAN魔法演示</button>
                <button class="btn" onclick="showVLANBenefits()">VLAN优点展示</button>
                <button class="btn" onclick="resetDemo()">重置演示</button>
            </div>
        </div>

        <div class="section quiz-section">
            <h2 class="section-title">🎯 做题时间！</h2>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
            <div class="quiz-question">
                以下关于VLAN的叙述中，属于其优点的是（ ）。
            </div>
            <div class="quiz-options">
                <div class="quiz-option" onclick="selectAnswer('A')">
                    <strong>A.</strong> 允许逻辑地划分网段
                </div>
                <div class="quiz-option" onclick="selectAnswer('B')">
                    <strong>B.</strong> 减少了冲突域的数量
                </div>
                <div class="quiz-option" onclick="selectAnswer('C')">
                    <strong>C.</strong> 增加了冲突域的大小
                </div>
                <div class="quiz-option" onclick="selectAnswer('D')">
                    <strong>D.</strong> 减少了广播域的数量
                </div>
            </div>
            <div class="explanation" id="answerExplanation" style="display: none;">
                <h3>📚 详细解析</h3>
                <p><strong>正确答案：A</strong></p>
                <p>VLAN的核心优点就是<strong>允许逻辑地划分网段</strong>！让我们通过动画来理解：</p>
                <ul style="margin: 15px 0; padding-left: 20px;">
                    <li>🎯 <strong>逻辑划分</strong>：不受物理位置限制，可以灵活组织设备</li>
                    <li>🛡️ <strong>安全隔离</strong>：不同VLAN之间默认无法直接通信</li>
                    <li>📡 <strong>广播控制</strong>：每个VLAN是独立的广播域</li>
                    <li>🔧 <strong>管理便利</strong>：可以根据部门、功能等逻辑分组</li>
                </ul>
            </div>
        </div>

        <div class="section">
            <h2 class="section-title">🧠 知识要点总结</h2>
            <div class="explanation">
                <h3>🔍 关键概念理解：</h3>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin: 20px 0;">
                    <div style="background: #e3f2fd; padding: 20px; border-radius: 10px;">
                        <h4>🌐 广播域 vs 冲突域</h4>
                        <p><strong>广播域</strong>：广播消息能到达的范围</p>
                        <p><strong>冲突域</strong>：数据冲突可能发生的范围</p>
                        <p>交换机消除冲突域，VLAN划分广播域</p>
                    </div>
                    <div style="background: #f3e5f5; padding: 20px; border-radius: 10px;">
                        <h4>✨ VLAN的本质</h4>
                        <p>在<strong>同一台交换机</strong>上创建多个<strong>逻辑网络</strong></p>
                        <p>每个VLAN = 一个独立的广播域</p>
                        <p>不同VLAN需要路由器才能通信</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 页面加载动画
        window.addEventListener('load', function() {
            gsap.timeline()
                .to('.title', { duration: 1, opacity: 1, y: 0, ease: 'back.out(1.7)' })
                .to('.subtitle', { duration: 0.8, opacity: 1, y: 0, ease: 'power2.out' }, '-=0.5')
                .to('.section', {
                    duration: 0.6,
                    opacity: 1,
                    y: 0,
                    stagger: 0.2,
                    ease: 'power2.out'
                }, '-=0.3');
        });

        // Canvas 网络演示
        const canvas = document.getElementById('networkCanvas');
        const ctx = canvas.getContext('2d');

        function resizeCanvas() {
            const container = canvas.parentElement;
            canvas.width = container.offsetWidth;
            canvas.height = container.offsetHeight;
        }

        resizeCanvas();
        window.addEventListener('resize', resizeCanvas);

        // 网络设备类
        class NetworkDevice {
            constructor(x, y, type, label) {
                this.x = x;
                this.y = y;
                this.type = type;
                this.label = label;
                this.vlan = null;
                this.isActive = false;
            }

            draw() {
                ctx.save();

                // 设备颜色
                const colors = {
                    computer: '#4CAF50',
                    switch: '#2196F3',
                    router: '#FF9800'
                };

                ctx.fillStyle = colors[this.type];

                if (this.isActive) {
                    ctx.shadowColor = colors[this.type];
                    ctx.shadowBlur = 20;
                }

                // 绘制设备
                if (this.type === 'computer') {
                    ctx.beginPath();
                    ctx.arc(this.x, this.y, 25, 0, Math.PI * 2);
                    ctx.fill();
                } else if (this.type === 'switch') {
                    ctx.fillRect(this.x - 35, this.y - 15, 70, 30);
                } else if (this.type === 'router') {
                    ctx.fillRect(this.x - 30, this.y - 20, 60, 40);
                }

                // 标签
                ctx.fillStyle = 'white';
                ctx.font = '12px Arial';
                ctx.textAlign = 'center';
                ctx.fillText(this.label, this.x, this.y + 5);

                ctx.restore();
            }
        }

        // 网络设备数组
        let devices = [];
        let connections = [];
        let animationId;

        function initNetwork() {
            devices = [
                new NetworkDevice(150, 150, 'computer', 'PC1'),
                new NetworkDevice(300, 100, 'computer', 'PC2'),
                new NetworkDevice(450, 150, 'computer', 'PC3'),
                new NetworkDevice(600, 100, 'computer', 'PC4'),
                new NetworkDevice(375, 200, 'switch', 'SW1'),
                new NetworkDevice(750, 200, 'computer', 'PC5'),
                new NetworkDevice(900, 150, 'computer', 'PC6')
            ];

            connections = [
                [0, 4], [1, 4], [2, 4], [3, 4], [5, 4], [6, 4]
            ];
        }

        function drawConnections() {
            ctx.strokeStyle = '#666';
            ctx.lineWidth = 2;

            connections.forEach(([from, to]) => {
                const device1 = devices[from];
                const device2 = devices[to];

                ctx.beginPath();
                ctx.moveTo(device1.x, device1.y);
                ctx.lineTo(device2.x, device2.y);
                ctx.stroke();
            });
        }

        function animate() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            drawConnections();
            devices.forEach(device => device.draw());

            animationId = requestAnimationFrame(animate);
        }

        function showBasicNetwork() {
            initNetwork();
            animate();

            // 添加说明文字
            setTimeout(() => {
                ctx.fillStyle = '#333';
                ctx.font = '16px Arial';
                ctx.textAlign = 'left';
                ctx.fillText('基础网络：所有设备连接到同一个交换机', 20, 30);
            }, 500);
        }

        function showBroadcastStorm() {
            // 广播风暴动画
            let waveRadius = 0;
            const maxRadius = 200;

            function drawBroadcastWave() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                drawConnections();
                devices.forEach(device => device.draw());

                // 绘制广播波
                ctx.strokeStyle = '#ff4444';
                ctx.lineWidth = 3;
                ctx.globalAlpha = 1 - (waveRadius / maxRadius);

                devices.forEach(device => {
                    if (device.type === 'computer') {
                        ctx.beginPath();
                        ctx.arc(device.x, device.y, waveRadius, 0, Math.PI * 2);
                        ctx.stroke();
                    }
                });

                ctx.globalAlpha = 1;

                waveRadius += 3;
                if (waveRadius > maxRadius) {
                    waveRadius = 0;
                }

                requestAnimationFrame(drawBroadcastWave);
            }

            drawBroadcastWave();

            // 添加警告文字
            ctx.fillStyle = '#ff4444';
            ctx.font = 'bold 16px Arial';
            ctx.textAlign = 'left';
            ctx.fillText('⚠️ 广播风暴：所有设备都在同一个广播域中！', 20, 30);
        }

        // 交互功能
        function demonstrateSwitch() {
            showBasicNetwork();

            // 高亮交换机
            setTimeout(() => {
                devices[4].isActive = true;

                ctx.fillStyle = '#333';
                ctx.font = '14px Arial';
                ctx.textAlign = 'left';
                ctx.fillText('交换机连接所有设备，但它们都在同一个广播域中', 20, 50);
            }, 1000);
        }

        function showBroadcastProblem() {
            showBroadcastStorm();
        }

        function showVLANMagic() {
            initNetwork();

            // 为设备分配VLAN
            devices[0].vlan = 1; // PC1 -> VLAN 1
            devices[1].vlan = 1; // PC2 -> VLAN 1
            devices[2].vlan = 2; // PC3 -> VLAN 2
            devices[3].vlan = 2; // PC4 -> VLAN 2
            devices[5].vlan = 3; // PC5 -> VLAN 3
            devices[6].vlan = 3; // PC6 -> VLAN 3

            function drawVLANs() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);

                // 绘制VLAN分组
                const vlanColors = {
                    1: 'rgba(255, 107, 107, 0.3)',
                    2: 'rgba(78, 205, 196, 0.3)',
                    3: 'rgba(69, 183, 209, 0.3)'
                };

                // 绘制VLAN区域
                Object.keys(vlanColors).forEach(vlan => {
                    const vlanDevices = devices.filter(d => d.vlan == vlan);
                    if (vlanDevices.length > 0) {
                        ctx.fillStyle = vlanColors[vlan];
                        ctx.strokeStyle = vlanColors[vlan].replace('0.3', '0.8');
                        ctx.lineWidth = 3;
                        ctx.setLineDash([10, 5]);

                        // 计算包围框
                        const minX = Math.min(...vlanDevices.map(d => d.x)) - 50;
                        const maxX = Math.max(...vlanDevices.map(d => d.x)) + 50;
                        const minY = Math.min(...vlanDevices.map(d => d.y)) - 50;
                        const maxY = Math.max(...vlanDevices.map(d => d.y)) + 50;

                        ctx.fillRect(minX, minY, maxX - minX, maxY - minY);
                        ctx.strokeRect(minX, minY, maxX - minX, maxY - minY);

                        // VLAN标签
                        ctx.fillStyle = '#333';
                        ctx.font = 'bold 14px Arial';
                        ctx.setLineDash([]);
                        ctx.fillText(`VLAN ${vlan}`, minX + 10, minY + 20);
                    }
                });

                drawConnections();
                devices.forEach(device => device.draw());

                // 说明文字
                ctx.fillStyle = '#333';
                ctx.font = '16px Arial';
                ctx.textAlign = 'left';
                ctx.fillText('✨ VLAN魔法：逻辑划分网段，每个VLAN是独立的广播域', 20, 30);
            }

            drawVLANs();
        }

        function showVLANBenefits() {
            showVLANMagic();

            setTimeout(() => {
                ctx.fillStyle = '#4CAF50';
                ctx.font = 'bold 14px Arial';
                ctx.textAlign = 'left';
                ctx.fillText('✅ VLAN优点：', 20, canvas.height - 80);
                ctx.fillText('• 逻辑划分网段（不受物理位置限制）', 20, canvas.height - 60);
                ctx.fillText('• 提高安全性（VLAN间隔离）', 20, canvas.height - 40);
                ctx.fillText('• 减少广播风暴影响', 20, canvas.height - 20);
            }, 1000);
        }

        function resetDemo() {
            if (animationId) {
                cancelAnimationFrame(animationId);
            }
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            devices = [];
            connections = [];
        }

        // 题目交互
        let selectedAnswer = null;

        function selectAnswer(answer) {
            selectedAnswer = answer;

            // 重置所有选项
            document.querySelectorAll('.quiz-option').forEach(option => {
                option.classList.remove('correct', 'wrong');
            });

            // 标记选择的答案
            const options = document.querySelectorAll('.quiz-option');
            const selectedOption = options[answer.charCodeAt(0) - 65]; // A=0, B=1, C=2, D=3

            if (answer === 'A') {
                selectedOption.classList.add('correct');
                showCorrectFeedback();
            } else {
                selectedOption.classList.add('wrong');
                // 同时显示正确答案
                options[0].classList.add('correct');
                showIncorrectFeedback();
            }

            // 显示解析
            document.getElementById('answerExplanation').style.display = 'block';

            // 更新进度条
            document.getElementById('progressFill').style.width = '100%';

            // 滚动到解析
            setTimeout(() => {
                document.getElementById('answerExplanation').scrollIntoView({
                    behavior: 'smooth'
                });
            }, 500);
        }

        function showCorrectFeedback() {
            // 正确答案的庆祝动画
            gsap.to('.quiz-option.correct', {
                duration: 0.5,
                scale: 1.05,
                boxShadow: '0 10px 30px rgba(76, 175, 80, 0.4)',
                yoyo: true,
                repeat: 1
            });

            // 添加成功音效（如果需要）
            console.log('🎉 答对了！');
        }

        function showIncorrectFeedback() {
            // 错误答案的摇摆动画
            gsap.to('.quiz-option.wrong', {
                duration: 0.1,
                x: -10,
                yoyo: true,
                repeat: 5
            });

            console.log('❌ 答错了，再想想看！');
        }

        // 初始化
        showBasicNetwork();
    </script>
</body>
</html>
