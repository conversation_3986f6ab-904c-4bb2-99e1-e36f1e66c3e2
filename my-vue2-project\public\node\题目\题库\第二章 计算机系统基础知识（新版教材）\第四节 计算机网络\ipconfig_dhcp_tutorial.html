<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>IPConfig 与 DHCP 交互教学</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .section {
            background: rgba(255, 255, 255, 0.95);
            margin: 30px 0;
            padding: 40px;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            opacity: 0;
            transform: translateY(50px);
        }

        .question-box {
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            color: white;
            padding: 30px;
            border-radius: 15px;
            margin-bottom: 30px;
            text-align: center;
        }

        .question-title {
            font-size: 28px;
            font-weight: bold;
            margin-bottom: 20px;
        }

        .question-content {
            font-size: 18px;
            line-height: 1.6;
            margin-bottom: 20px;
        }

        .options {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
            margin: 20px 0;
        }

        .option {
            background: rgba(255, 255, 255, 0.2);
            padding: 15px;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }

        .option:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }

        .option.correct {
            border-color: #00d2d3;
            background: rgba(0, 210, 211, 0.3);
        }

        .canvas-container {
            display: flex;
            justify-content: center;
            margin: 30px 0;
        }

        canvas {
            border: 2px solid #ddd;
            border-radius: 10px;
            background: #f8f9fa;
        }

        .interactive-demo {
            background: #f8f9fa;
            padding: 30px;
            border-radius: 15px;
            margin: 20px 0;
            text-align: center;
        }

        .cmd-simulator {
            background: #1e1e1e;
            color: #00ff00;
            padding: 20px;
            border-radius: 10px;
            font-family: 'Courier New', monospace;
            margin: 20px 0;
            min-height: 200px;
            position: relative;
            overflow: hidden;
        }

        .cmd-line {
            margin: 5px 0;
            opacity: 0;
        }

        .cursor {
            display: inline-block;
            width: 10px;
            height: 20px;
            background: #00ff00;
            animation: blink 1s infinite;
        }

        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0; }
        }

        .btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
        }

        .concept-card {
            background: white;
            padding: 25px;
            border-radius: 15px;
            margin: 15px 0;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            border-left: 5px solid #667eea;
        }

        .highlight {
            background: linear-gradient(120deg, #a8edea 0%, #fed6e3 100%);
            padding: 3px 8px;
            border-radius: 5px;
            font-weight: bold;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e0e0e0;
            border-radius: 4px;
            overflow: hidden;
            margin: 20px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            width: 0%;
            transition: width 0.5s ease;
        }

        .step-indicator {
            display: flex;
            justify-content: center;
            margin: 30px 0;
        }

        .step {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #e0e0e0;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 10px;
            font-weight: bold;
            transition: all 0.3s ease;
        }

        .step.active {
            background: #667eea;
            color: white;
            transform: scale(1.2);
        }

        .step.completed {
            background: #00d2d3;
            color: white;
        }

        h2 {
            color: #667eea;
            font-size: 32px;
            margin-bottom: 20px;
            text-align: center;
        }

        h3 {
            color: #764ba2;
            font-size: 24px;
            margin-bottom: 15px;
        }

        .network-diagram {
            display: flex;
            justify-content: space-around;
            align-items: center;
            margin: 30px 0;
            flex-wrap: wrap;
        }

        .device {
            background: white;
            padding: 20px;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            text-align: center;
            margin: 10px;
            min-width: 150px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .device:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
        }

        .device-icon {
            font-size: 48px;
            margin-bottom: 10px;
        }

        .quiz-container {
            background: #f8f9fa;
            padding: 30px;
            border-radius: 15px;
            margin: 20px 0;
        }

        .feedback {
            padding: 15px;
            border-radius: 10px;
            margin: 15px 0;
            font-weight: bold;
            text-align: center;
            opacity: 0;
            transform: translateY(20px);
        }

        .feedback.correct {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .feedback.incorrect {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 题目展示区域 -->
        <div class="section" id="question-section">
            <div class="question-box">
                <div class="question-title">📝 考试题目</div>
                <div class="question-content">
                    在windows命令行窗口中使用（ ）命令可以查看本机DHCP服务是否已启用。
                </div>
                <div class="options">
                    <div class="option" data-answer="A">A. ipconfig</div>
                    <div class="option" data-answer="B">B. ipconfig/all</div>
                    <div class="option" data-answer="C">C. ipconfig/renew</div>
                    <div class="option" data-answer="D">D. ipconfig/release</div>
                </div>
                <div style="margin-top: 20px;">
                    <strong>正确答案：B</strong><br>
                    <strong>你的答案：A</strong>
                </div>
            </div>
        </div>

        <!-- 进度指示器 -->
        <div class="step-indicator">
            <div class="step active" data-step="1">1</div>
            <div class="step" data-step="2">2</div>
            <div class="step" data-step="3">3</div>
            <div class="step" data-step="4">4</div>
            <div class="step" data-step="5">5</div>
        </div>

        <div class="progress-bar">
            <div class="progress-fill" id="progress"></div>
        </div>

        <!-- 第一步：什么是网络和IP地址 -->
        <div class="section" id="step1">
            <h2>🌐 第一步：理解网络基础</h2>
            
            <div class="concept-card">
                <h3>什么是网络？</h3>
                <p>想象一下，网络就像一个巨大的邮政系统。每台电脑都需要一个<span class="highlight">地址</span>，这样其他电脑才能找到它并发送信息。</p>
            </div>

            <div class="network-diagram" id="network-demo">
                <div class="device" data-device="computer">
                    <div class="device-icon">💻</div>
                    <div>你的电脑</div>
                    <div style="font-size: 12px; color: #666;">需要IP地址</div>
                </div>
                <div class="device" data-device="router">
                    <div class="device-icon">📡</div>
                    <div>路由器</div>
                    <div style="font-size: 12px; color: #666;">分配IP地址</div>
                </div>
                <div class="device" data-device="internet">
                    <div class="device-icon">🌍</div>
                    <div>互联网</div>
                    <div style="font-size: 12px; color: #666;">全球网络</div>
                </div>
            </div>

            <div class="interactive-demo">
                <h3>🎮 互动体验：点击设备了解更多</h3>
                <div id="device-info" style="min-height: 100px; padding: 20px; background: white; border-radius: 10px; margin-top: 15px;">
                    点击上面的设备图标来了解它们的作用！
                </div>
            </div>

            <button class="btn" onclick="nextStep(2)">理解了，继续学习 →</button>
        </div>

        <!-- 第二步：什么是DHCP -->
        <div class="section" id="step2" style="display: none;">
            <h2>🔧 第二步：认识DHCP服务</h2>

            <div class="concept-card">
                <h3>什么是DHCP？</h3>
                <p><span class="highlight">DHCP</span>（Dynamic Host Configuration Protocol，动态主机配置协议）就像一个自动化的IP地址分配员。</p>
            </div>

            <div class="canvas-container">
                <canvas id="dhcp-canvas" width="600" height="300"></canvas>
            </div>

            <div class="interactive-demo">
                <h3>🎮 DHCP工作流程演示</h3>
                <button class="btn" onclick="startDHCPDemo()">开始演示</button>
                <button class="btn" onclick="resetDHCPDemo()">重置</button>
                <div id="dhcp-status" style="margin-top: 15px; font-size: 18px; font-weight: bold;"></div>
            </div>

            <div class="concept-card">
                <h3>DHCP的好处：</h3>
                <ul style="margin-left: 20px; line-height: 1.8;">
                    <li>🚀 <strong>自动化</strong>：无需手动设置IP地址</li>
                    <li>🔄 <strong>动态分配</strong>：设备离开时IP地址可以重新分配给其他设备</li>
                    <li>⚡ <strong>即插即用</strong>：新设备连接网络时自动获得配置</li>
                    <li>🛡️ <strong>避免冲突</strong>：确保每个设备都有唯一的IP地址</li>
                </ul>
            </div>

            <button class="btn" onclick="nextStep(3)">明白了DHCP，继续 →</button>
        </div>

        <!-- 第三步：认识ipconfig命令 -->
        <div class="section" id="step3" style="display: none;">
            <h2>💻 第三步：认识ipconfig命令</h2>

            <div class="concept-card">
                <h3>什么是ipconfig？</h3>
                <p><span class="highlight">ipconfig</span>是Windows系统中用来查看和管理网络配置的命令行工具。就像医生用听诊器检查你的心跳一样，ipconfig帮我们"检查"网络的状态。</p>
            </div>

            <div class="cmd-simulator" id="cmd-demo">
                <div>Microsoft Windows [版本 10.0.19041.1]</div>
                <div>(c) Microsoft Corporation. 保留所有权利。</div>
                <div></div>
                <div>C:\Users\<USER>\\Users\\Student&gt; ${command}</div>
            `;

            // 逐行显示输出
            output.forEach((line, index) => {
                setTimeout(() => {
                    const lineDiv = document.createElement('div');
                    lineDiv.className = 'cmd-line';
                    lineDiv.textContent = line;

                    // 高亮DHCP相关行
                    if (line.includes('DHCP 已启用')) {
                        lineDiv.style.background = 'rgba(255, 255, 0, 0.3)';
                        lineDiv.style.fontWeight = 'bold';
                    }

                    cmdDiv.appendChild(lineDiv);
                    gsap.fromTo(lineDiv,
                        { opacity: 0, x: -20 },
                        { opacity: 1, x: 0, duration: 0.3 }
                    );
                }, index * 200);
            });

            // 添加新的命令提示符
            setTimeout(() => {
                const promptDiv = document.createElement('div');
                promptDiv.innerHTML = 'C:\\Users\\<USER>\n\n这就是为什么我们需要使用 ipconfig/all 命令的原因 - 它显示了完整的DHCP配置信息。');
                            }, 500);
                        }
                    } else if (!isDHCP) {
                        // 错误反馈
                        line.style.background = 'rgba(255, 107, 107, 0.3)';
                        gsap.to(line, {
                            x: [-5, 5, -5, 5, 0],
                            duration: 0.5
                        });
                        setTimeout(() => {
                            line.style.background = '';
                        }, 1000);
                    }
                });

                line.addEventListener('mouseenter', () => {
                    if (!line.classList.contains('found')) {
                        line.style.background = 'rgba(255, 255, 255, 0.1)';
                    }
                });

                line.addEventListener('mouseleave', () => {
                    if (!line.classList.contains('found')) {
                        line.style.background = '';
                    }
                });
            });
        }

        // 最终测验
        function initFinalQuiz() {
            document.querySelectorAll('#final-quiz .option').forEach(option => {
                option.addEventListener('click', () => {
                    const answer = option.dataset.final;
                    const feedback = document.getElementById('final-feedback');

                    // 移除之前的选择
                    document.querySelectorAll('#final-quiz .option').forEach(opt => {
                        opt.classList.remove('correct', 'incorrect');
                    });

                    if (answer === 'B') {
                        option.classList.add('correct');
                        feedback.className = 'feedback correct';
                        feedback.textContent = '🎉 正确！ipconfig/all 命令可以显示详细的网络配置信息，包括DHCP是否已启用。你已经完全掌握了这个知识点！';
                    } else {
                        option.classList.add('incorrect');
                        feedback.className = 'feedback incorrect';
                        let explanation = '';
                        switch(answer) {
                            case 'A':
                                explanation = '❌ ipconfig 只显示基本信息，看不到DHCP状态。';
                                break;
                            case 'C':
                                explanation = '❌ ipconfig/renew 是用来重新获取IP地址的，不是查看DHCP状态。';
                                break;
                            case 'D':
                                explanation = '❌ ipconfig/release 是用来释放IP地址的，不是查看DHCP状态。';
                                break;
                        }
                        feedback.textContent = explanation + ' 正确答案是B：ipconfig/all';
                    }

                    gsap.to(feedback, {
                        opacity: 1,
                        y: 0,
                        duration: 0.5,
                        ease: "power2.out"
                    });
                });
            });
        }

        // 重新开始教程
        function restartTutorial() {
            currentStep = 1;
            updateProgress();
            updateStepIndicator();

            // 隐藏所有步骤
            document.querySelectorAll('[id^="step"]').forEach(step => {
                step.style.display = 'none';
            });

            // 显示第一步
            document.getElementById('step1').style.display = 'block';

            // 滚动到顶部
            window.scrollTo({ top: 0, behavior: 'smooth' });

            // 重置所有交互状态
            document.getElementById('device-info').innerHTML = '点击上面的设备图标来了解它们的作用！';
        }

        // 初始化
        updateProgress();
        updateStepIndicator();

        // 题目选项点击效果（仅用于展示）
        document.querySelectorAll('.question-box .option').forEach(option => {
            option.addEventListener('click', () => {
                document.querySelectorAll('.question-box .option').forEach(opt => {
                    opt.classList.remove('correct');
                });
                if (option.dataset.answer === 'B') {
                    option.classList.add('correct');
                }
            });
        });
    </script>
</body>
</html>
