<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>网络安全协议学习 - 交互式教学</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            opacity: 0;
            animation: fadeInUp 1s ease-out forwards;
        }

        .title {
            font-size: 3rem;
            color: white;
            margin-bottom: 20px;
            text-shadow: 0 4px 8px rgba(0,0,0,0.3);
        }

        .subtitle {
            font-size: 1.2rem;
            color: rgba(255,255,255,0.8);
            margin-bottom: 40px;
        }

        .learning-section {
            background: white;
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            opacity: 0;
            animation: fadeInUp 1s ease-out 0.3s forwards;
        }

        .section-title {
            font-size: 2rem;
            color: #333;
            margin-bottom: 30px;
            text-align: center;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        .network-layers {
            display: flex;
            justify-content: center;
            margin: 40px 0;
            gap: 20px;
        }

        .layer {
            width: 150px;
            height: 80px;
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .layer::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            transition: left 0.5s ease;
        }

        .layer:hover::before {
            left: 100%;
        }

        .layer-7 { background: #ff6b6b; }
        .layer-4 { background: #4ecdc4; }
        .layer-3 { background: #45b7d1; }
        .layer-2 { background: #96ceb4; }

        .layer:hover {
            transform: translateY(-5px) scale(1.05);
            box-shadow: 0 10px 20px rgba(0,0,0,0.2);
        }

        .layer.active {
            transform: scale(1.1);
            box-shadow: 0 0 30px rgba(69, 183, 209, 0.6);
        }

        .protocol-demo {
            margin: 40px 0;
            text-align: center;
        }

        .protocol-card {
            display: inline-block;
            width: 200px;
            height: 120px;
            margin: 15px;
            border-radius: 15px;
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .protocol-card:hover {
            transform: translateY(-10px);
        }

        .ipsec { background: linear-gradient(135deg, #667eea, #764ba2); }
        .l2tp { background: linear-gradient(135deg, #96ceb4, #4ecdc4); }
        .pap { background: linear-gradient(135deg, #ffeaa7, #fdcb6e); }
        .https { background: linear-gradient(135deg, #fd79a8, #e84393); }

        .protocol-name {
            font-size: 1.5rem;
            font-weight: bold;
            margin-bottom: 10px;
            padding-top: 20px;
        }

        .protocol-layer {
            font-size: 0.9rem;
            opacity: 0.8;
        }

        .question-section {
            background: white;
            border-radius: 20px;
            padding: 40px;
            margin-top: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            opacity: 0;
            animation: fadeInUp 1s ease-out 0.6s forwards;
        }

        .question {
            font-size: 1.3rem;
            color: #333;
            margin-bottom: 30px;
            line-height: 1.6;
        }

        .options {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 30px;
        }

        .option {
            padding: 20px;
            border: 2px solid #e0e0e0;
            border-radius: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
            background: #f8f9fa;
            position: relative;
            overflow: hidden;
        }

        .option:hover {
            border-color: #667eea;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .option.correct {
            border-color: #28a745;
            background: #d4edda;
            animation: correctAnswer 0.5s ease;
        }

        .option.wrong {
            border-color: #dc3545;
            background: #f8d7da;
            animation: wrongAnswer 0.5s ease;
        }

        .canvas-container {
            margin: 40px 0;
            text-align: center;
        }

        #animationCanvas {
            border: 2px solid #e0e0e0;
            border-radius: 15px;
            background: white;
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }

        .explanation {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 30px;
            margin-top: 30px;
            border-left: 5px solid #667eea;
            opacity: 0;
            transform: translateY(20px);
            transition: all 0.5s ease;
        }

        .explanation.show {
            opacity: 1;
            transform: translateY(0);
        }

        .btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1.1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes correctAnswer {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        @keyframes wrongAnswer {
            0% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
            100% { transform: translateX(0); }
        }

        .floating-particles {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
        }

        .particle {
            position: absolute;
            width: 4px;
            height: 4px;
            background: rgba(255,255,255,0.3);
            border-radius: 50%;
            animation: float 6s infinite linear;
        }

        @keyframes float {
            0% {
                transform: translateY(100vh) rotate(0deg);
                opacity: 0;
            }
            10% {
                opacity: 1;
            }
            90% {
                opacity: 1;
            }
            100% {
                transform: translateY(-100px) rotate(360deg);
                opacity: 0;
            }
        }
    </style>
</head>
<body>
    <div class="floating-particles" id="particles"></div>
    
    <div class="container">
        <div class="header">
            <h1 class="title">🔐 网络安全协议学习</h1>
            <p class="subtitle">通过动画和交互理解网络层安全协议</p>
        </div>

        <div class="learning-section">
            <h2 class="section-title">📚 知识讲解：网络协议分层</h2>
            <p style="text-align: center; margin-bottom: 30px; font-size: 1.1rem; color: #666;">
                点击下面的网络层来了解不同层级的安全协议
            </p>
            
            <div class="network-layers">
                <div class="layer layer-7" data-layer="7">
                    <div>应用层<br><small>Layer 7</small></div>
                </div>
                <div class="layer layer-4" data-layer="4">
                    <div>传输层<br><small>Layer 4</small></div>
                </div>
                <div class="layer layer-3" data-layer="3">
                    <div>网络层<br><small>Layer 3</small></div>
                </div>
                <div class="layer layer-2" data-layer="2">
                    <div>数据链路层<br><small>Layer 2</small></div>
                </div>
            </div>

            <div class="canvas-container">
                <canvas id="animationCanvas" width="800" height="400"></canvas>
            </div>

            <div class="protocol-demo">
                <h3 style="margin-bottom: 20px; color: #333;">🔍 点击协议卡片了解详情</h3>
                <div class="protocol-card ipsec" data-protocol="ipsec">
                    <div class="protocol-name">IPSec</div>
                    <div class="protocol-layer">网络层协议</div>
                </div>
                <div class="protocol-card l2tp" data-protocol="l2tp">
                    <div class="protocol-name">L2TP</div>
                    <div class="protocol-layer">数据链路层协议</div>
                </div>
                <div class="protocol-card pap" data-protocol="pap">
                    <div class="protocol-name">PAP</div>
                    <div class="protocol-layer">数据链路层协议</div>
                </div>
                <div class="protocol-card https" data-protocol="https">
                    <div class="protocol-name">HTTPS</div>
                    <div class="protocol-layer">应用层协议</div>
                </div>
            </div>
        </div>

        <div class="question-section">
            <h2 class="section-title">🤔 练习题目</h2>
            <div class="question">
                下列安全协议中（ ）是网络层安全协议。
            </div>
            
            <div class="options">
                <div class="option" data-answer="A">
                    <strong>A. IPSec</strong><br>
                    <small>Internet Protocol Security</small>
                </div>
                <div class="option" data-answer="B">
                    <strong>B. L2TP</strong><br>
                    <small>Layer 2 Tunneling Protocol</small>
                </div>
                <div class="option" data-answer="C">
                    <strong>C. PAP</strong><br>
                    <small>Password Authentication Protocol</small>
                </div>
                <div class="option" data-answer="D">
                    <strong>D. HTTPS</strong><br>
                    <small>Hypertext Transfer Protocol Secure</small>
                </div>
            </div>

            <div style="text-align: center;">
                <button class="btn" onclick="showAnswer()">💡 查看答案解析</button>
                <button class="btn" onclick="resetQuestion()">🔄 重新答题</button>
            </div>

            <div class="explanation" id="explanation">
                <h3 style="color: #28a745; margin-bottom: 15px;">✅ 正确答案：A - IPSec</h3>
                <p><strong>详细解析：</strong></p>
                <ul style="margin: 15px 0; padding-left: 20px;">
                    <li><strong>IPSec</strong>：工作在网络层（第3层），提供IP数据包的加密和认证</li>
                    <li><strong>L2TP</strong>：工作在数据链路层（第2层），主要用于VPN隧道</li>
                    <li><strong>PAP</strong>：工作在数据链路层（第2层），PPP协议的认证方式</li>
                    <li><strong>HTTPS</strong>：工作在应用层（第7层），基于SSL/TLS的安全HTTP</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        // 创建浮动粒子效果
        function createParticles() {
            const particlesContainer = document.getElementById('particles');
            for (let i = 0; i < 20; i++) {
                const particle = document.createElement('div');
                particle.className = 'particle';
                particle.style.left = Math.random() * 100 + '%';
                particle.style.animationDelay = Math.random() * 6 + 's';
                particle.style.animationDuration = (Math.random() * 3 + 3) + 's';
                particlesContainer.appendChild(particle);
            }
        }

        // Canvas动画
        const canvas = document.getElementById('animationCanvas');
        const ctx = canvas.getContext('2d');
        let animationId;
        let currentAnimation = 'default';

        // 动画状态
        let animationTime = 0;
        const protocols = {
            ipsec: { x: 100, y: 200, color: '#667eea', layer: 3 },
            l2tp: { x: 300, y: 200, color: '#4ecdc4', layer: 2 },
            pap: { x: 500, y: 200, color: '#fdcb6e', layer: 2 },
            https: { x: 700, y: 200, color: '#e84393', layer: 7 }
        };

        function drawNetworkStack() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 绘制网络层级
            const layers = [
                { name: '应用层', y: 50, color: '#ff6b6b' },
                { name: '传输层', y: 120, color: '#4ecdc4' },
                { name: '网络层', y: 190, color: '#45b7d1' },
                { name: '数据链路层', y: 260, color: '#96ceb4' }
            ];

            layers.forEach((layer, index) => {
                ctx.fillStyle = layer.color;
                ctx.fillRect(50, layer.y, 700, 50);
                ctx.fillStyle = 'white';
                ctx.font = 'bold 16px Microsoft YaHei';
                ctx.textAlign = 'center';
                ctx.fillText(layer.name, 400, layer.y + 30);
            });

            // 绘制协议位置
            Object.keys(protocols).forEach(key => {
                const protocol = protocols[key];
                const layerY = 50 + (7 - protocol.layer) * 70 + 25;
                
                ctx.fillStyle = protocol.color;
                ctx.beginPath();
                ctx.arc(protocol.x, layerY, 20, 0, Math.PI * 2);
                ctx.fill();
                
                ctx.fillStyle = 'white';
                ctx.font = 'bold 12px Microsoft YaHei';
                ctx.textAlign = 'center';
                ctx.fillText(key.toUpperCase(), protocol.x, layerY + 4);
            });
        }

        function animate() {
            animationTime += 0.02;
            drawNetworkStack();
            
            // 添加脉冲效果
            if (currentAnimation !== 'default') {
                const protocol = protocols[currentAnimation];
                const layerY = 50 + (7 - protocol.layer) * 70 + 25;
                
                ctx.strokeStyle = protocol.color;
                ctx.lineWidth = 3;
                ctx.globalAlpha = 0.5 + 0.5 * Math.sin(animationTime * 5);
                ctx.beginPath();
                ctx.arc(protocol.x, layerY, 30 + 10 * Math.sin(animationTime * 3), 0, Math.PI * 2);
                ctx.stroke();
                ctx.globalAlpha = 1;
            }
            
            animationId = requestAnimationFrame(animate);
        }

        // 事件监听
        document.querySelectorAll('.layer').forEach(layer => {
            layer.addEventListener('click', function() {
                document.querySelectorAll('.layer').forEach(l => l.classList.remove('active'));
                this.classList.add('active');
                
                const layerNum = this.dataset.layer;
                highlightLayer(layerNum);
            });
        });

        document.querySelectorAll('.protocol-card').forEach(card => {
            card.addEventListener('click', function() {
                const protocol = this.dataset.protocol;
                currentAnimation = protocol;
                showProtocolInfo(protocol);
            });
        });

        function highlightLayer(layerNum) {
            // 高亮显示对应层级的协议
            const layerProtocols = Object.keys(protocols).filter(key => 
                protocols[key].layer == layerNum
            );
            
            if (layerProtocols.length > 0) {
                currentAnimation = layerProtocols[0];
            }
        }

        function showProtocolInfo(protocol) {
            const info = {
                ipsec: {
                    title: 'IPSec - 网络层安全协议',
                    description: 'IPSec工作在网络层（第3层），为IP数据包提供加密、认证和完整性保护。它是VPN技术的核心协议。'
                },
                l2tp: {
                    title: 'L2TP - 数据链路层隧道协议',
                    description: 'L2TP工作在数据链路层（第2层），主要用于创建VPN隧道，对PPP协议进行扩展。'
                },
                pap: {
                    title: 'PAP - 密码认证协议',
                    description: 'PAP工作在数据链路层（第2层），是PPP协议的一种简单认证方式，以明文传输密码。'
                },
                https: {
                    title: 'HTTPS - 安全超文本传输协议',
                    description: 'HTTPS工作在应用层（第7层），基于SSL/TLS协议为HTTP通信提供加密保护。'
                }
            };

            alert(info[protocol].title + '\n\n' + info[protocol].description);
        }

        // 题目交互
        let answered = false;

        document.querySelectorAll('.option').forEach(option => {
            option.addEventListener('click', function() {
                if (answered) return;
                
                const answer = this.dataset.answer;
                answered = true;
                
                if (answer === 'A') {
                    this.classList.add('correct');
                    setTimeout(() => {
                        document.getElementById('explanation').classList.add('show');
                    }, 500);
                } else {
                    this.classList.add('wrong');
                    document.querySelector('[data-answer="A"]').classList.add('correct');
                    setTimeout(() => {
                        document.getElementById('explanation').classList.add('show');
                    }, 1000);
                }
            });
        });

        function showAnswer() {
            if (!answered) {
                document.querySelector('[data-answer="A"]').classList.add('correct');
                answered = true;
            }
            document.getElementById('explanation').classList.add('show');
        }

        function resetQuestion() {
            answered = false;
            document.querySelectorAll('.option').forEach(option => {
                option.classList.remove('correct', 'wrong');
            });
            document.getElementById('explanation').classList.remove('show');
        }

        // 初始化
        createParticles();
        animate();
        drawNetworkStack();
    </script>
</body>
</html>
